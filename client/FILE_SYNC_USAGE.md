# File Sync Service Usage Guide

This document explains how to use the new File Sync functionality that allows you to:
1. Get all files in the monitored folder
2. Compare them with a supplied list of known files
3. Process and send only the new files to the server

## Services Overview

### FileComparisonService
Handles file comparison operations:
- `getAllFilesInMonitoredFolder()` - Gets all files in the monitored folder
- `compareFiles(List<String> suppliedList)` - Compares folder files with supplied list
- `getNewFiles(List<String> suppliedList)` - Gets files in folder but not in supplied list
- `getMissingFiles(List<String> suppliedList)` - Gets files in supplied list but not in folder

### FileSyncService
Handles the complete sync process:
- `syncNewFilesToServer(List<String> knownFiles)` - Syncs only new files to server
- `syncAllFilesToServer()` - Syncs all files to server
- `isConnectedToServer()` - Check WebSocket connection status
- `ensureServerConnection()` - Ensure connection to server

## REST API Endpoints

### File Comparison Endpoints
```
GET /api/files/all
- Returns: List of all files in monitored folder

POST /api/files/compare
- Body: ["file1.txt", "file2.txt", ...]
- Returns: FileComparisonResult with new, missing, and common files

POST /api/files/new
- Body: ["file1.txt", "file2.txt", ...]
- Returns: List of files in folder but not in supplied list

POST /api/files/missing
- Body: ["file1.txt", "file2.txt", ...]
- Returns: List of files in supplied list but not in folder
```

### File Sync Endpoints
```
POST /api/sync/new
- Body: ["known_file1.txt", "known_file2.txt", ...]
- Returns: Result with list of successfully synced new files

POST /api/sync/all
- Returns: Result with list of all successfully synced files

GET /api/sync/status
- Returns: Server connection status

POST /api/sync/connect
- Returns: Result of connection attempt
```

## Usage Examples

### 1. Using the Service Directly (Java)

```java
@Autowired
private FileSyncService fileSyncService;

// Sync only new files
List<String> knownFiles = Arrays.asList("file1.txt", "file2.txt");
List<String> syncedFiles = fileSyncService.syncNewFilesToServer(knownFiles);
System.out.println("Synced files: " + syncedFiles);

// Sync all files
List<String> allSyncedFiles = fileSyncService.syncAllFilesToServer();
System.out.println("All synced files: " + allSyncedFiles);
```

### 2. Using REST API (curl examples)

```bash
# Get all files in monitored folder
curl -X GET http://localhost:8080/api/files/all

# Compare files
curl -X POST http://localhost:8080/api/files/compare \
  -H "Content-Type: application/json" \
  -d '["file1.txt", "file2.txt", "old_file.txt"]'

# Sync new files to server
curl -X POST http://localhost:8080/api/sync/new \
  -H "Content-Type: application/json" \
  -d '["file1.txt", "file2.txt"]'

# Check connection status
curl -X GET http://localhost:8080/api/sync/status

# Sync all files
curl -X POST http://localhost:8080/api/sync/all
```

### 3. Response Examples

#### File Comparison Response
```json
{
  "newFiles": ["new_file1.txt", "new_file2.txt"],
  "missingFiles": ["missing_file.txt"],
  "commonFiles": ["common_file.txt"]
}
```

#### Sync Response
```json
{
  "success": true,
  "message": "Successfully synced 2 files",
  "syncedFiles": ["new_file1.txt", "new_file2.txt"]
}
```

#### Status Response
```json
{
  "connected": true,
  "message": "Connected to server"
}
```

## Configuration

The service uses the existing configuration from `application.properties`:

```properties
# Folder to monitor
monitor.folder=${file.input-dir}

# WebSocket server URL
websocket.url=ws://localhost:8090/app
```

## Error Handling

The service handles various error scenarios:
- File not found or not readable
- WebSocket connection issues
- File processing failures
- IO errors

All errors are logged and appropriate responses are returned via the API.

## Testing

Run the tests to verify functionality:

```bash
# Run specific test
mvn test -Dtest=FileSyncServiceTest

# Run all tests
mvn test
```

## Demo

To run the demo examples:

```bash
# File comparison demo
java -jar client.jar demo

# File sync demo
java -jar client.jar sync-demo
```
