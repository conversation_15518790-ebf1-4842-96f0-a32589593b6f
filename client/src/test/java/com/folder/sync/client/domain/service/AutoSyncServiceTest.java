package com.folder.sync.client.domain.service;

import com.folder.sync.client.domain.model.ServerFileListMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AutoSyncServiceTest {

    @Mock
    private FileSyncService fileSyncService;

    private AutoSyncService autoSyncService;

    @BeforeEach
    void setUp() {
        autoSyncService = new AutoSyncService(fileSyncService);
    }

    @Test
    void testHandleServerFileListMessage_Success() throws Exception {
        // Arrange
        List<String> serverFiles = Arrays.asList("file1.txt", "file2.txt");
        List<String> syncedFiles = Arrays.asList("file3.txt", "file4.txt");
        ServerFileListMessage message = new ServerFileListMessage(serverFiles, "Test message");
        
        when(fileSyncService.syncNewFilesToServer(serverFiles)).thenReturn(syncedFiles);

        // Act
        autoSyncService.handleServerFileListMessage(message);

        // Assert
        verify(fileSyncService).syncNewFilesToServer(serverFiles);
    }

    @Test
    void testHandleServerFileListMessage_NoNewFiles() throws Exception {
        // Arrange
        List<String> serverFiles = Arrays.asList("file1.txt", "file2.txt");
        List<String> syncedFiles = Arrays.asList(); // No new files
        ServerFileListMessage message = new ServerFileListMessage(serverFiles);
        
        when(fileSyncService.syncNewFilesToServer(serverFiles)).thenReturn(syncedFiles);

        // Act
        autoSyncService.handleServerFileListMessage(message);

        // Assert
        verify(fileSyncService).syncNewFilesToServer(serverFiles);
    }

    @Test
    void testHandleServerFileListMessage_Exception() throws Exception {
        // Arrange
        List<String> serverFiles = Arrays.asList("file1.txt", "file2.txt");
        ServerFileListMessage message = new ServerFileListMessage(serverFiles);
        
        when(fileSyncService.syncNewFilesToServer(anyList())).thenThrow(new RuntimeException("Sync failed"));

        // Act
        autoSyncService.handleServerFileListMessage(message);

        // Assert
        verify(fileSyncService).syncNewFilesToServer(serverFiles);
        // Should not throw exception - it should be caught and logged
    }
}
