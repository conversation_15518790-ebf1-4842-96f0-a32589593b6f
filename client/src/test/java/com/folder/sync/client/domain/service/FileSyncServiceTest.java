package com.folder.sync.client.domain.service;

import com.folder.sync.client.domain.model.FileEvent;
import com.folder.sync.client.domain.model.FileProcessingResult;
import com.folder.sync.client.infrastructure.websocket.client.ClientSessionHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FileSyncServiceTest {

    @Mock
    private FileComparisonService fileComparisonService;

    @Mock
    private FileProcessingService fileProcessingService;

    @Mock
    private ClientSessionHandler clientSessionHandler;

    private FileSyncService fileSyncService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        fileSyncService = new FileSyncService(fileComparisonService, fileProcessingService, clientSessionHandler);
        ReflectionTestUtils.setField(fileSyncService, "folderPath", tempDir.toString());
    }

    @Test
    void testSyncNewFilesToServer_Success() throws IOException {
        // Create test files
        Files.writeString(tempDir.resolve("file1.txt"), "content1");
        Files.writeString(tempDir.resolve("file2.txt"), "content2");

        // Mock dependencies
        List<String> knownFiles = Arrays.asList("existing.txt");
        List<String> newFiles = Arrays.asList("file1.txt", "file2.txt");
        
        when(fileComparisonService.getNewFiles(knownFiles)).thenReturn(newFiles);
        when(clientSessionHandler.isConnected()).thenReturn(true);
        when(fileProcessingService.processFile(any(FileEvent.class)))
            .thenReturn(new FileProcessingResult(true, "Success", new FileEvent()));
        when(clientSessionHandler.sendToServer(any(FileProcessingResult.class)))
            .thenReturn("receipt123");

        // Execute
        List<String> result = fileSyncService.syncNewFilesToServer(knownFiles);

        // Verify
        assertEquals(2, result.size());
        assertTrue(result.contains("file1.txt"));
        assertTrue(result.contains("file2.txt"));
        
        verify(fileComparisonService).getNewFiles(knownFiles);
        verify(fileProcessingService, times(2)).processFile(any(FileEvent.class));
        verify(clientSessionHandler, times(2)).sendToServer(any(FileProcessingResult.class));
    }

    @Test
    void testSyncNewFilesToServer_NoNewFiles() throws IOException {
        List<String> knownFiles = Arrays.asList("file1.txt", "file2.txt");
        
        when(fileComparisonService.getNewFiles(knownFiles)).thenReturn(Arrays.asList());

        List<String> result = fileSyncService.syncNewFilesToServer(knownFiles);

        assertEquals(0, result.size());
        verify(fileComparisonService).getNewFiles(knownFiles);
        verify(fileProcessingService, never()).processFile(any(FileEvent.class));
        verify(clientSessionHandler, never()).sendToServer(any(FileProcessingResult.class));
    }

    @Test
    void testSyncNewFilesToServer_NotConnected() throws IOException {
        // Create test file
        Files.writeString(tempDir.resolve("file1.txt"), "content1");

        List<String> knownFiles = Arrays.asList();
        List<String> newFiles = Arrays.asList("file1.txt");

        when(fileComparisonService.getNewFiles(knownFiles)).thenReturn(newFiles);
        when(clientSessionHandler.isConnected()).thenReturn(false);
        when(fileProcessingService.processFile(any(FileEvent.class)))
            .thenReturn(new FileProcessingResult(true, "Success", new FileEvent()));

        List<String> result = fileSyncService.syncNewFilesToServer(knownFiles);

        assertEquals(0, result.size());
        verify(fileComparisonService).getNewFiles(knownFiles);
        verify(clientSessionHandler, never()).sendToServer(any(FileProcessingResult.class));
    }

    @Test
    void testSyncNewFilesToServer_ProcessingFailure() throws IOException {
        // Create test file
        Files.writeString(tempDir.resolve("file1.txt"), "content1");

        List<String> knownFiles = Arrays.asList();
        List<String> newFiles = Arrays.asList("file1.txt");

        when(fileComparisonService.getNewFiles(knownFiles)).thenReturn(newFiles);
        when(fileProcessingService.processFile(any(FileEvent.class)))
            .thenReturn(new FileProcessingResult(false, "Processing failed", null));

        List<String> result = fileSyncService.syncNewFilesToServer(knownFiles);

        assertEquals(0, result.size());
        verify(fileComparisonService).getNewFiles(knownFiles);
        verify(fileProcessingService).processFile(any(FileEvent.class));
        verify(clientSessionHandler, never()).sendToServer(any(FileProcessingResult.class));
    }

    @Test
    void testSyncAllFilesToServer_Success() throws IOException {
        // Create test files
        Files.writeString(tempDir.resolve("file1.txt"), "content1");
        Files.writeString(tempDir.resolve("file2.txt"), "content2");

        List<String> allFiles = Arrays.asList("file1.txt", "file2.txt");
        
        when(fileComparisonService.getAllFilesInMonitoredFolder()).thenReturn(allFiles);
        when(clientSessionHandler.isConnected()).thenReturn(true);
        when(fileProcessingService.processFile(any(FileEvent.class)))
            .thenReturn(new FileProcessingResult(true, "Success", new FileEvent()));
        when(clientSessionHandler.sendToServer(any(FileProcessingResult.class)))
            .thenReturn("receipt123");

        List<String> result = fileSyncService.syncAllFilesToServer();

        assertEquals(2, result.size());
        assertTrue(result.contains("file1.txt"));
        assertTrue(result.contains("file2.txt"));
        
        verify(fileComparisonService).getAllFilesInMonitoredFolder();
        verify(fileProcessingService, times(2)).processFile(any(FileEvent.class));
        verify(clientSessionHandler, times(2)).sendToServer(any(FileProcessingResult.class));
    }

    @Test
    void testIsConnectedToServer() {
        when(clientSessionHandler.isConnected()).thenReturn(true);
        
        assertTrue(fileSyncService.isConnectedToServer());
        
        when(clientSessionHandler.isConnected()).thenReturn(false);
        
        assertFalse(fileSyncService.isConnectedToServer());
    }

    @Test
    void testEnsureServerConnection_AlreadyConnected() throws Exception {
        when(clientSessionHandler.isConnected()).thenReturn(true);

        assertTrue(fileSyncService.ensureServerConnection());

        verify(clientSessionHandler, never()).connect();
    }

    @Test
    void testEnsureServerConnection_ConnectSuccess() throws Exception {
        when(clientSessionHandler.isConnected()).thenReturn(false);
        when(clientSessionHandler.connect()).thenReturn(true);

        assertTrue(fileSyncService.ensureServerConnection());

        verify(clientSessionHandler).connect();
    }

    @Test
    void testEnsureServerConnection_ConnectFailure() throws Exception {
        when(clientSessionHandler.isConnected()).thenReturn(false);
        when(clientSessionHandler.connect()).thenReturn(false);

        assertFalse(fileSyncService.ensureServerConnection());

        verify(clientSessionHandler).connect();
    }

    @Test
    void testEnsureServerConnection_ConnectException() throws Exception {
        when(clientSessionHandler.isConnected()).thenReturn(false);
        when(clientSessionHandler.connect()).thenThrow(new RuntimeException("Connection failed"));

        assertFalse(fileSyncService.ensureServerConnection());

        verify(clientSessionHandler).connect();
    }
}
