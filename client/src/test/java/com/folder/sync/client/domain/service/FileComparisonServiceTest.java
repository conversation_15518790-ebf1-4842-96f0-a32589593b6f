package com.folder.sync.client.domain.service;

import com.folder.sync.client.domain.model.FileComparisonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class FileComparisonServiceTest {

    private FileComparisonService fileComparisonService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        fileComparisonService = new FileComparisonService();
        // Set the monitor folder to our temp directory
        ReflectionTestUtils.setField(fileComparisonService, "folderPath", tempDir.toString());
    }

    @Test
    void testGetAllFilesInMonitoredFolder() throws IOException {
        // Create test files
        Files.createFile(tempDir.resolve("file1.txt"));
        Files.createFile(tempDir.resolve("file2.txt"));
        Files.createFile(tempDir.resolve("file3.txt"));

        List<String> files = fileComparisonService.getAllFilesInMonitoredFolder();

        assertEquals(3, files.size());
        assertTrue(files.contains("file1.txt"));
        assertTrue(files.contains("file2.txt"));
        assertTrue(files.contains("file3.txt"));
    }

    @Test
    void testCompareFiles() throws IOException {
        // Create test files in folder
        Files.createFile(tempDir.resolve("file1.txt"));
        Files.createFile(tempDir.resolve("file2.txt"));
        Files.createFile(tempDir.resolve("file3.txt"));

        // Supplied list has some overlap and some differences
        List<String> suppliedList = Arrays.asList("file2.txt", "file3.txt", "file4.txt", "file5.txt");

        FileComparisonResult result = fileComparisonService.compareFiles(suppliedList);

        // New files: in folder but not in supplied list
        assertEquals(1, result.getNewFiles().size());
        assertTrue(result.getNewFiles().contains("file1.txt"));

        // Missing files: in supplied list but not in folder
        assertEquals(2, result.getMissingFiles().size());
        assertTrue(result.getMissingFiles().contains("file4.txt"));
        assertTrue(result.getMissingFiles().contains("file5.txt"));

        // Common files: in both
        assertEquals(2, result.getCommonFiles().size());
        assertTrue(result.getCommonFiles().contains("file2.txt"));
        assertTrue(result.getCommonFiles().contains("file3.txt"));
    }

    @Test
    void testGetNewFiles() throws IOException {
        // Create test files in folder
        Files.createFile(tempDir.resolve("file1.txt"));
        Files.createFile(tempDir.resolve("file2.txt"));

        List<String> suppliedList = Arrays.asList("file2.txt", "file3.txt");

        List<String> newFiles = fileComparisonService.getNewFiles(suppliedList);

        assertEquals(1, newFiles.size());
        assertTrue(newFiles.contains("file1.txt"));
    }

    @Test
    void testGetMissingFiles() throws IOException {
        // Create test files in folder
        Files.createFile(tempDir.resolve("file1.txt"));
        Files.createFile(tempDir.resolve("file2.txt"));

        List<String> suppliedList = Arrays.asList("file2.txt", "file3.txt", "file4.txt");

        List<String> missingFiles = fileComparisonService.getMissingFiles(suppliedList);

        assertEquals(2, missingFiles.size());
        assertTrue(missingFiles.contains("file3.txt"));
        assertTrue(missingFiles.contains("file4.txt"));
    }

    @Test
    void testEmptyFolder() throws IOException {
        List<String> suppliedList = Arrays.asList("file1.txt", "file2.txt");

        FileComparisonResult result = fileComparisonService.compareFiles(suppliedList);

        assertTrue(result.getNewFiles().isEmpty());
        assertEquals(2, result.getMissingFiles().size());
        assertTrue(result.getCommonFiles().isEmpty());
    }

    @Test
    void testEmptySuppliedList() throws IOException {
        // Create test files in folder
        Files.createFile(tempDir.resolve("file1.txt"));
        Files.createFile(tempDir.resolve("file2.txt"));

        List<String> suppliedList = Arrays.asList();

        FileComparisonResult result = fileComparisonService.compareFiles(suppliedList);

        assertEquals(2, result.getNewFiles().size());
        assertTrue(result.getMissingFiles().isEmpty());
        assertTrue(result.getCommonFiles().isEmpty());
    }
}
