package com.folder.sync.client.domain.service;

import com.folder.sync.client.domain.model.FileComparisonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class FileComparisonService {

    @Value("${monitor.folder}")
    private String folderPath;

    /**
     * Gets all file names in the monitored folder
     * 
     * @return List of file names (not full paths) in the monitored folder
     * @throws IOException if there's an error reading the folder
     */
    public List<String> getAllFilesInMonitoredFolder() throws IOException {
        List<String> fileNames = new ArrayList<>();
        Path folder = Paths.get(folderPath);
        
        if (!Files.exists(folder)) {
            log.warn("Monitored folder does not exist: {}", folderPath);
            return fileNames;
        }
        
        if (!Files.isDirectory(folder)) {
            log.warn("Monitored path is not a directory: {}", folderPath);
            return fileNames;
        }
        
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(folder)) {
            for (Path entry : stream) {
                if (Files.isRegularFile(entry)) {
                    fileNames.add(entry.getFileName().toString());
                }
            }
        }
        
        log.debug("Found {} files in monitored folder: {}", fileNames.size(), folderPath);
        return fileNames;
    }

    /**
     * Compares files in the monitored folder with a supplied list of file names
     * 
     * @param suppliedFileList List of file names to compare against
     * @return FileComparisonResult containing the differences
     * @throws IOException if there's an error reading the folder
     */
    public FileComparisonResult compareFiles(List<String> suppliedFileList) throws IOException {
        List<String> folderFiles = getAllFilesInMonitoredFolder();
        
        Set<String> folderFileSet = new HashSet<>(folderFiles);
        Set<String> suppliedFileSet = new HashSet<>(suppliedFileList);
        
        // Files in folder but not in supplied list (new files)
        List<String> newFiles = new ArrayList<>();
        for (String file : folderFileSet) {
            if (!suppliedFileSet.contains(file)) {
                newFiles.add(file);
            }
        }
        
        // Files in supplied list but not in folder (missing files)
        List<String> missingFiles = new ArrayList<>();
        for (String file : suppliedFileSet) {
            if (!folderFileSet.contains(file)) {
                missingFiles.add(file);
            }
        }
        
        // Files in both (common files)
        List<String> commonFiles = new ArrayList<>();
        for (String file : folderFileSet) {
            if (suppliedFileSet.contains(file)) {
                commonFiles.add(file);
            }
        }
        
        log.info("File comparison completed. New: {}, Missing: {}, Common: {}", 
                newFiles.size(), missingFiles.size(), commonFiles.size());
        
        return new FileComparisonResult(newFiles, missingFiles, commonFiles);
    }

    /**
     * Gets only the difference - files that are in the monitored folder but not in the supplied list
     * 
     * @param suppliedFileList List of file names to compare against
     * @return List of file names that are new (in folder but not in supplied list)
     * @throws IOException if there's an error reading the folder
     */
    public List<String> getNewFiles(List<String> suppliedFileList) throws IOException {
        FileComparisonResult result = compareFiles(suppliedFileList);
        return result.getNewFiles();
    }

    /**
     * Gets only the missing files - files that are in the supplied list but not in the monitored folder
     * 
     * @param suppliedFileList List of file names to compare against
     * @return List of file names that are missing (in supplied list but not in folder)
     * @throws IOException if there's an error reading the folder
     */
    public List<String> getMissingFiles(List<String> suppliedFileList) throws IOException {
        FileComparisonResult result = compareFiles(suppliedFileList);
        return result.getMissingFiles();
    }
}
