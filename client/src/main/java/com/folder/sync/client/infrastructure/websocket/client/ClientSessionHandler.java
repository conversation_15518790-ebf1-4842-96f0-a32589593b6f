package com.folder.sync.client.infrastructure.websocket.client;

import com.folder.sync.client.domain.model.FileEvent;
import com.folder.sync.client.domain.model.FileProcessingResult;
import com.folder.sync.client.domain.model.ServerFileListMessage;
import com.folder.sync.client.infrastructure.websocket.client.model.ClientConnectionStatus;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.stomp.*;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.messaging.WebSocketStompClient;

import java.lang.reflect.Type;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Handler for WebSocket session events.
 * This handler processes connection events and incoming messages.
 * Dev note: you can not use async here
 */
@Getter
@Setter
@Component
@Slf4j
public class ClientSessionHandler extends StompSessionHandlerAdapter {
    private final ApplicationEventPublisher publisher;
    private final WebSocketStompClient stompClient;

    @Value("${websocket.url}")
    private String websocketUrl;

    @Value("${system.name}")
    private String systemName;

    private StompSession stompSession;
    private boolean isConnected = false;
    private Thread connectionThread;

    public ClientSessionHandler(
            ApplicationEventPublisher publisher
    ) {
        WebSocketClient webSocketClient = new StandardWebSocketClient();
        this.stompClient = new WebSocketStompClient(webSocketClient);
        this.stompClient.setMessageConverter(new MappingJackson2MessageConverter());

        this.publisher = publisher;
    }

    private void connectAndSubscribe(String url) {
        WebSocketHttpHeaders wsh = new WebSocketHttpHeaders();
        StompHeaders headers = new StompHeaders();

        headers.add("Content-Type", "application/json");
        headers.add("Accept", "application/json");
        headers.add("user", systemName);

        try {
            log.info("Attempting to connect to WebSocket at: {}", url);
            stompClient.connectAsync(url, wsh, headers, this).get();
        } catch (Exception e) {
            log.error("❌ Connection error: {}", e.getMessage());
        }
    }

    public boolean connect() throws InterruptedException, ExecutionException {
        log.debug("Connecting to {} with user {}", websocketUrl, systemName);

        connectAndSubscribe(websocketUrl);
        if (this.isConnected()) {
            log.info("Connected to {} with user {}", websocketUrl, systemName);
            this.getStompSession().getSessionId();
        } else
            log.debug("Failed to connect to {} with user {}", websocketUrl, systemName);

        return isConnected();
    }

    @Override
    public void afterConnected(@NonNull StompSession session, @NonNull StompHeaders connectedHeaders) {
        isConnected = true;
        this.stompSession = session;

        // Subscribe to file list messages from server
        subscribeToFileList();
    }

    @Override
    public void handleException(@NonNull StompSession session, StompCommand command, @NonNull StompHeaders headers,@NonNull byte[] payload, @NonNull Throwable exception) {
        publisher.publishEvent(new ClientConnectionStatus(session.isConnected(), exception.getMessage()));
        log.error("❌Error in client WebSocket session: {}", exception.getMessage());
    }

    @Override
    public void handleTransportError(@NonNull StompSession session, @NonNull Throwable exception) {
        log.error("❌Client Transport error: {}", exception.getMessage());
        publisher.publishEvent(new ClientConnectionStatus(session.isConnected(), exception.getMessage()));
        isConnected = false;
        if (connectionThread == null || !connectionThread.isAlive() ) {
            if (connectionThread != null)
                log.error(connectionThread.getState().toString());
            connectionThread = attemptReconnect();
        }
    }

    public synchronized void sendPrivateMessage(String destination, Object message) {
        if (stompSession.isConnected()) {
            stompSession.send(destination, message);
        } else {
            log.warn("Sending message to {} failed, session is not connected", destination);
        }
    }

    public synchronized void subscribe(String topic, Class<?> classType) {
        if (isConnected) {
            stompSession.subscribe(topic, new StompFrameHandler() {
                @Override
                @NonNull
                public Type getPayloadType(@NonNull StompHeaders headers) {
                    return classType;
                }

                @Override
                public void handleFrame(@NonNull StompHeaders headers, @Nullable Object payload) {
                    log.info("New incoming message");
                    log.debug("Message details: {}", payload);
                    try {
                        if (payload == null)
                            return;

                        Object message = classType.cast(payload);
                        publisher.publishEvent(message);
                    } catch (ClassCastException e) {
                        log.error("❌Failed to cast STOMP message to {}: {}", classType.getName(), e.getMessage());
                    }
                }
            });
        } else {
            log.warn("Subscription to {} failed", topic);
        }
    }


    public String sendToServer(FileProcessingResult file) {
        try {
            String destination = "/app/sync";
            log.debug("Sending message to destination: {}", destination);
            var receiptId = stompSession.send(destination, file).getReceiptId();
            log.info("Sent message to server with receipt ID: {}", receiptId);
            return receiptId;
        } catch (Exception e) {
            log.error("Error sending message to server: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Subscribe to file list messages from the server
     */
    private void subscribeToFileList() {
        if (isConnected && stompSession != null) {
            log.info("Subscribing to personalized file list messages from server for user: {}", systemName);
            // Subscribe to user-specific queue for personalized file lists
            subscribe("/user/queue/filelist", ServerFileListMessage.class);

            // Also subscribe to general topic for backward compatibility
            subscribe("/topic/filelist", ServerFileListMessage.class);
        } else {
            log.warn("Cannot subscribe to file list - not connected");
        }
    }

    private Thread attemptReconnect() {
        var thread = new Thread(() -> {
            int retryCount = 0;
            while (!isConnected && retryCount < 10) {  // Retry up to 10 times
                try {
                    int waitTime = (int) Math.pow(2, retryCount); // Exponential backoff: 2^retryCount seconds
                    log.info("🔄 Reconnecting in {} seconds...", waitTime);
                    TimeUnit.SECONDS.sleep(waitTime);

                    connect();
                    if (isConnected) {
                        log.info("✅ Reconnected successfully!");
                        break;
                    }
                } catch (Exception e) {
                    log.error("❌ Reconnection attempt {} failed: {}", retryCount, e.getMessage());
                }
                retryCount++;
            }

            if (!isConnected) {
                log.error("🚨 Unable to reconnect after multiple attempts.");
            }
        });

        thread.start();
        return thread;
    }
}
