package com.folder.sync.client.infrastructure.web;

import com.folder.sync.client.domain.service.FileSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/sync")
@RequiredArgsConstructor
public class FileSyncController {

    private final FileSyncService fileSyncService;

    /**
     * Sync new files (files not in the supplied list) to the server
     */
    @PostMapping("/new")
    public ResponseEntity<Map<String, Object>> syncNewFiles(@RequestBody List<String> knownFileList) {
        try {
            log.info("Received request to sync new files. Known files count: {}", knownFileList.size());
            
            // Ensure connection to server
            if (!fileSyncService.ensureServerConnection()) {
                return ResponseEntity.status(503).body(Map.of(
                    "success", false,
                    "message", "Unable to connect to server",
                    "syncedFiles", List.of()
                ));
            }
            
            List<String> syncedFiles = fileSyncService.syncNewFilesToServer(knownFileList);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", String.format("Successfully synced %d files", syncedFiles.size()),
                "syncedFiles", syncedFiles
            ));
            
        } catch (IOException e) {
            log.error("IO error during sync: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "IO error: " + e.getMessage(),
                "syncedFiles", List.of()
            ));
        } catch (Exception e) {
            log.error("Unexpected error during sync: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Unexpected error: " + e.getMessage(),
                "syncedFiles", List.of()
            ));
        }
    }

    /**
     * Sync all files in the monitored folder to the server
     */
    @PostMapping("/all")
    public ResponseEntity<Map<String, Object>> syncAllFiles() {
        try {
            log.info("Received request to sync all files");
            
            // Ensure connection to server
            if (!fileSyncService.ensureServerConnection()) {
                return ResponseEntity.status(503).body(Map.of(
                    "success", false,
                    "message", "Unable to connect to server",
                    "syncedFiles", List.of()
                ));
            }
            
            List<String> syncedFiles = fileSyncService.syncAllFilesToServer();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", String.format("Successfully synced %d files", syncedFiles.size()),
                "syncedFiles", syncedFiles
            ));
            
        } catch (IOException e) {
            log.error("IO error during sync: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "IO error: " + e.getMessage(),
                "syncedFiles", List.of()
            ));
        } catch (Exception e) {
            log.error("Unexpected error during sync: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Unexpected error: " + e.getMessage(),
                "syncedFiles", List.of()
            ));
        }
    }

    /**
     * Check server connection status
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        boolean connected = fileSyncService.isConnectedToServer();
        
        return ResponseEntity.ok(Map.of(
            "connected", connected,
            "message", connected ? "Connected to server" : "Not connected to server"
        ));
    }

    /**
     * Attempt to connect to the server
     */
    @PostMapping("/connect")
    public ResponseEntity<Map<String, Object>> connect() {
        try {
            boolean success = fileSyncService.ensureServerConnection();
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Successfully connected to server"
                ));
            } else {
                return ResponseEntity.status(503).body(Map.of(
                    "success", false,
                    "message", "Failed to connect to server"
                ));
            }
            
        } catch (Exception e) {
            log.error("Error connecting to server: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Error connecting to server: " + e.getMessage()
            ));
        }
    }
}
