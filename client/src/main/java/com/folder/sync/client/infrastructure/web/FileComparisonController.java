package com.folder.sync.client.infrastructure.web;

import com.folder.sync.client.domain.model.FileComparisonResult;
import com.folder.sync.client.domain.service.FileComparisonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
public class FileComparisonController {

    private final FileComparisonService fileComparisonService;

    /**
     * Get all files in the monitored folder
     */
    @GetMapping("/all")
    public ResponseEntity<List<String>> getAllFiles() {
        try {
            List<String> files = fileComparisonService.getAllFilesInMonitoredFolder();
            return ResponseEntity.ok(files);
        } catch (IOException e) {
            log.error("Error getting all files: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Compare files in monitored folder with supplied list
     */
    @PostMapping("/compare")
    public ResponseEntity<FileComparisonResult> compareFiles(@RequestBody List<String> suppliedFileList) {
        try {
            FileComparisonResult result = fileComparisonService.compareFiles(suppliedFileList);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("Error comparing files: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get only new files (files in folder but not in supplied list)
     */
    @PostMapping("/new")
    public ResponseEntity<List<String>> getNewFiles(@RequestBody List<String> suppliedFileList) {
        try {
            List<String> newFiles = fileComparisonService.getNewFiles(suppliedFileList);
            return ResponseEntity.ok(newFiles);
        } catch (IOException e) {
            log.error("Error getting new files: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get only missing files (files in supplied list but not in folder)
     */
    @PostMapping("/missing")
    public ResponseEntity<List<String>> getMissingFiles(@RequestBody List<String> suppliedFileList) {
        try {
            List<String> missingFiles = fileComparisonService.getMissingFiles(suppliedFileList);
            return ResponseEntity.ok(missingFiles);
        } catch (IOException e) {
            log.error("Error getting missing files: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
