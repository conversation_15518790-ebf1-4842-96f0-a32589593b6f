package com.folder.sync.client.infrastructure.folder;

import com.folder.sync.client.domain.model.FileEvent;
import com.folder.sync.client.domain.model.FileProcessingResult;
import com.folder.sync.client.domain.service.FileProcessingService;
import com.folder.sync.client.infrastructure.websocket.client.ClientSessionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class FolderMonitorService {

    @Value("${monitor.folder}")
    private String folderPath;

    @Value("${monitor.retry.max-attempts}")
    private int maxRetryAttempts;

    @Value("${monitor.retry.delay-ms}")
    private long retryDelayMs;

    @Value("${monitor.stability-check.max-attempts}")
    private int stabilityCheckAttempts;

    @Value("${monitor.stability-check.delay-ms}")
    private long stabilityCheckDelayMs;

    @Value("${system.name")
    private String systemName;

    @Value("${current.exams}")
    private String currentExams;

    private final FileProcessingService fileProcessingService;
    private final ClientSessionHandler webSocketClientService;
    private final AtomicBoolean isMonitoring = new AtomicBoolean(false);
    private WatchService watchService;

    public FolderMonitorService(FileProcessingService fileProcessingService, ClientSessionHandler webSocketClientService) {
        this.fileProcessingService = fileProcessingService;
        this.webSocketClientService = webSocketClientService;
    }

    @Scheduled(fixedDelay = 1000)
    public void startMonitoring() {
        if (!webSocketClientService.isConnected()) {
            log.info("connecting  to server");
            try {
                webSocketClientService.connect();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        }
        if (isMonitoring.compareAndSet(false, true)) {
            try {
                watchService = FileSystems.getDefault().newWatchService();
                Path path = Paths.get(folderPath);
                path.register(watchService, StandardWatchEventKinds.ENTRY_CREATE, StandardWatchEventKinds.ENTRY_MODIFY);
                
                monitorFolder();
            } catch (IOException e) {
                System.err.println("Failed to start folder monitoring: " + e.getMessage());
                isMonitoring.set(false);
            }
        }
    }

    private void monitorFolder() {
        Thread monitoringThread = new Thread(() -> {
            while (isMonitoring.get()) {
                try {
                    WatchKey key = watchService.take();
                    for (WatchEvent<?> event : key.pollEvents()) {
                        if (event.kind() == StandardWatchEventKinds.ENTRY_CREATE || event.kind() == StandardWatchEventKinds.ENTRY_MODIFY) {
                            Path filePath = (Path) event.context();
                            processFile(filePath);
                        }
                    }
                    key.reset();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    System.err.println("Error monitoring folder: " + e.getMessage());
                }
            }
        });
        monitoringThread.setDaemon(true);
        monitoringThread.start();
    }

    private void processFile(Path filePath) {
        try {
            // Wait for file stability
            if (!waitForFileStability(filePath)) {
                System.err.println("File is still being written: " + filePath);
                return;
            }

            // Read file content
            String content = new String(Files.readAllBytes(Path.of(folderPath + "/" + filePath)));
            
            // Create file event
            FileEvent fileEvent = new FileEvent();
            fileEvent.setFilePath(filePath.toString());
            fileEvent.setContent(content);
            fileEvent.setFileSize(Files.size(Path.of(folderPath + "/" + filePath)));
            fileEvent.setLastModified(Files.getLastModifiedTime(Path.of(folderPath + "/" + filePath)).toMillis());
            fileEvent.setExam(currentExams);
            fileEvent.setUser(systemName);

            // Process file
            FileProcessingResult result = fileProcessingService.processFile(fileEvent);
            
            // Send result via WebSocket
            webSocketClientService.sendToServer(result);

        } catch (IOException e) {
            System.err.println("Error processing file: " + e.getMessage());
        }
    }

    private boolean waitForFileStability(Path filePath) {
        long lastSize = -1;
        long currentSize;
        
        for (int i = 0; i < stabilityCheckAttempts; i++) {
            try {
                currentSize = Files.size(Path.of(folderPath + "/" + filePath));
                log.info("File still being written. Current size is : " + currentSize);
                if (currentSize == lastSize) {
                    return true;
                }
                lastSize = currentSize;
                Thread.sleep(stabilityCheckDelayMs);
            } catch (Exception e) {
                log.error("Error processing file: " + e.getMessage());
                return false;
            }
        }
        return false;
    }
}
