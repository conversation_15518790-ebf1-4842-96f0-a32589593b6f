package com.folder.sync.client.infrastructure.websocket.client;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.messaging.WebSocketStompClient;

/**
 * Configuration for WebSocket client.
 * This configuration creates beans for WebSocket client connections.
 */
@Configuration
public class WebSocketClientConfig {

    /**
     * Creates a StandardWebSocketClient bean.
     *
     * @return the StandardWebSocketClient as WebSocketClient
     */
    @Bean
    public WebSocketClient standardWebSocketClient() {
        return new StandardWebSocketClient();
    }

    /**
     * Creates a WebSocketStompClient bean with a message converter.
     *
     * @param standardWebSocketClient the WebSocketClient
     * @return the WebSocketStompClient
     */
    @Bean
    public WebSocketStompClient webSocketStompClient(WebSocketClient standardWebSocketClient) {
        WebSocketStompClient stompClient = new WebSocketStompClient(standardWebSocketClient);
        stompClient.setMessageConverter(new MappingJackson2MessageConverter());
        return stompClient;
    }
}
