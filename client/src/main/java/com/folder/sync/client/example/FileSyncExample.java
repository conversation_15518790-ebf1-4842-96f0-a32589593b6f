package com.folder.sync.client.example;

import com.folder.sync.client.domain.service.FileSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Example demonstrating how to use the FileSyncService
 * This will run on application startup and show example usage
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileSyncExample implements CommandLineRunner {

    private final FileSyncService fileSyncService;

    @Override
    public void run(String... args) throws Exception {
        // Only run the example if explicitly requested
        if (args.length > 0 && "sync-demo".equals(args[0])) {
            runSyncExample();
        }
    }

    private void runSyncExample() {
        try {
            log.info("=== File Sync Service Example ===");

            // Example 1: Check server connection
            log.info("1. Checking server connection...");
            boolean connected = fileSyncService.isConnectedToServer();
            log.info("Server connection status: {}", connected ? "Connected" : "Not connected");

            // Example 2: Ensure connection
            if (!connected) {
                log.info("2. Attempting to connect to server...");
                boolean connectionResult = fileSyncService.ensureServerConnection();
                log.info("Connection attempt result: {}", connectionResult ? "Success" : "Failed");
            }

            // Example 3: Sync new files (files not in known list)
            log.info("3. Syncing new files...");
            List<String> knownFiles = Arrays.asList("old_file1.txt", "old_file2.txt", "processed.txt");
            List<String> syncedNewFiles = fileSyncService.syncNewFilesToServer(knownFiles);
            log.info("Synced {} new files: {}", syncedNewFiles.size(), syncedNewFiles);

            // Example 4: Sync all files
            log.info("4. Syncing all files...");
            List<String> syncedAllFiles = fileSyncService.syncAllFilesToServer();
            log.info("Synced {} total files: {}", syncedAllFiles.size(), syncedAllFiles);

            log.info("=== Sync example completed ===");

        } catch (Exception e) {
            log.error("Error running file sync example: {}", e.getMessage(), e);
        }
    }
}
