package com.folder.sync.client.domain.service;

import com.folder.sync.client.domain.model.FileEvent;
import com.folder.sync.client.domain.model.FileProcessingResult;
import com.folder.sync.client.infrastructure.websocket.client.ClientSessionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileSyncService {

    private final FileComparisonService fileComparisonService;
    private final FileProcessingService fileProcessingService;
    private final ClientSessionHandler clientSessionHandler;

    @Value("${monitor.folder}")
    private String folderPath;

    /**
     * Syncs new files (files not in the supplied list) to the server
     * 
     * @param knownFileList List of files that are already known/processed
     * @return List of file names that were successfully synced
     * @throws IOException if there's an error reading files
     */
    public List<String> syncNewFilesToServer(List<String> knownFileList) throws IOException {
        log.info("Starting sync process for new files...");
        
        // Get files that are not in the known list
        List<String> newFiles = fileComparisonService.getNewFiles(knownFileList);
        log.info("Found {} new files to sync: {}", newFiles.size(), newFiles);
        
        if (newFiles.isEmpty()) {
            log.info("No new files to sync");
            return new ArrayList<>();
        }
        
        List<String> successfullySynced = new ArrayList<>();
        
        for (String fileName : newFiles) {
            try {
                if (processAndSendFile(fileName)) {
                    successfullySynced.add(fileName);
                    log.info("Successfully synced file: {}", fileName);
                } else {
                    log.warn("Failed to sync file: {}", fileName);
                }
            } catch (Exception e) {
                log.error("Error syncing file {}: {}", fileName, e.getMessage(), e);
            }
        }
        
        log.info("Sync completed. Successfully synced {} out of {} files", 
                successfullySynced.size(), newFiles.size());
        
        return successfullySynced;
    }

    /**
     * Processes a single file and sends it to the server
     * 
     * @param fileName Name of the file to process
     * @return true if successfully processed and sent, false otherwise
     */
    private boolean processAndSendFile(String fileName) {
        try {
            // Create full file path
            Path filePath = Paths.get(folderPath, fileName);
            
            if (!Files.exists(filePath)) {
                log.warn("File does not exist: {}", filePath);
                return false;
            }
            
            if (!Files.isRegularFile(filePath)) {
                log.warn("Path is not a regular file: {}", filePath);
                return false;
            }
            
            // Read file content
            String content = Files.readString(filePath);
            
            // Create FileEvent
            FileEvent fileEvent = new FileEvent();
            fileEvent.setFilePath(fileName);
            fileEvent.setContent(content);
            fileEvent.setFileSize(Files.size(filePath));
            fileEvent.setLastModified(Files.getLastModifiedTime(filePath).toMillis());
            
            log.debug("Created FileEvent for {}: size={}, lastModified={}", 
                    fileName, fileEvent.getFileSize(), fileEvent.getLastModified());
            
            // Process the file
            FileProcessingResult result = fileProcessingService.processFile(fileEvent);
            
            if (!result.isSuccess()) {
                log.error("File processing failed for {}: {}", fileName, result.getMessage());
                return false;
            }
            
            // Check if WebSocket is connected
            if (!clientSessionHandler.isConnected()) {
                log.warn("WebSocket is not connected. Cannot send file: {}", fileName);
                return false;
            }
            
            // Send to server
            String receiptId = clientSessionHandler.sendToServer(result);
            log.debug("Sent file {} to server with receipt ID: {}", fileName, receiptId);
            
            return true;
            
        } catch (IOException e) {
            log.error("IO error processing file {}: {}", fileName, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("Unexpected error processing file {}: {}", fileName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Syncs all files in the monitored folder to the server (regardless of known list)
     *
     * @return List of file names that were successfully synced
     * @throws IOException if there's an error reading files
     */
    public List<String> syncAllFilesToServer() throws IOException {
        log.info("Starting sync process for all files...");

        List<String> allFiles = fileComparisonService.getAllFilesInMonitoredFolder();
        log.info("Found {} files to sync: {}", allFiles.size(), allFiles);

        if (allFiles.isEmpty()) {
            log.info("No files to sync");
            return new ArrayList<>();
        }

        List<String> successfullySynced = new ArrayList<>();

        for (String fileName : allFiles) {
            try {
                if (processAndSendFile(fileName)) {
                    successfullySynced.add(fileName);
                    log.info("Successfully synced file: {}", fileName);
                } else {
                    log.warn("Failed to sync file: {}", fileName);
                }
            } catch (Exception e) {
                log.error("Error syncing file {}: {}", fileName, e.getMessage(), e);
            }
        }

        log.info("Sync completed. Successfully synced {} out of {} files",
                successfullySynced.size(), allFiles.size());

        return successfullySynced;
    }

    /**
     * Gets the connection status of the WebSocket client
     *
     * @return true if connected, false otherwise
     */
    public boolean isConnectedToServer() {
        return clientSessionHandler.isConnected();
    }

    /**
     * Attempts to connect to the server if not already connected
     *
     * @return true if connection successful, false otherwise
     */
    public boolean ensureServerConnection() {
        if (clientSessionHandler.isConnected()) {
            return true;
        }

        try {
            log.info("Attempting to connect to server...");
            return clientSessionHandler.connect();
        } catch (Exception e) {
            log.error("Failed to connect to server: {}", e.getMessage(), e);
            return false;
        }
    }
}
