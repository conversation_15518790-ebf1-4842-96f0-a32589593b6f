package com.folder.sync.client.infrastructure.websocket.client;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Properties for WebSocket client configuration.
 * These properties are set in application.properties file.
 */
@Configuration
@ConfigurationProperties(prefix = "websocket.client")
@Data
public class WebSocketClientProperties {

    /**
     * The server URL to connect to.
     * Default is ws://localhost:8080/ws
     */
    private String serverUrl = "ws://localhost:8080/ws";
    
    /**
     * The connection timeout in seconds.
     * Default is 5 seconds.
     */
    private int connectionTimeout = 5;
    
    /**
     * The topic to subscribe to for public messages.
     * Default is /topic/public
     */
    private String publicTopic = "/topic/public";
    
    /**
     * The topic to subscribe to for notifications.
     * Default is /topic/notifications
     */
    private String notificationsTopic = "/topic/notifications";
    
    /**
     * The topic to subscribe to for status updates.
     * Default is /topic/status
     */
    private String statusTopic = "/topic/status";
    
    /**
     * Whether to automatically reconnect on connection loss.
     * Default is true.
     */
    private boolean autoReconnect = true;
    
    /**
     * The reconnect interval in seconds.
     * Default is 5 seconds.
     */
    private int reconnectInterval = 5;
}
