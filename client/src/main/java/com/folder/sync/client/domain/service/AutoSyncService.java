package com.folder.sync.client.domain.service;

import com.folder.sync.client.domain.model.ServerFileListMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AutoSyncService {

    private final FileSyncService fileSyncService;

    /**
     * Handles incoming server file list messages and automatically triggers sync
     * 
     * @param serverFileListMessage The message containing the list of files the server has
     */
    @EventListener
    public void handleServerFileListMessage(ServerFileListMessage serverFileListMessage) {
        log.info("Received file list from server with {} files", 
                serverFileListMessage.getFileNames().size());
        
        try {
            // Automatically trigger sync for new files
            List<String> syncedFiles = fileSyncService.syncNewFilesToServer(
                serverFileListMessage.getFileNames()
            );
            
            if (syncedFiles.isEmpty()) {
                log.info("No new files to sync - client and server are in sync");
            } else {
                log.info("Auto-sync completed: successfully synced {} new files: {}", 
                        syncedFiles.size(), syncedFiles);
            }
            
        } catch (Exception e) {
            log.error("Error during auto-sync process: {}", e.getMessage(), e);
        }
    }
}
