package com.folder.sync.client.example;

import com.folder.sync.client.domain.model.FileComparisonResult;
import com.folder.sync.client.domain.service.FileComparisonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Example demonstrating how to use the FileComparisonService
 * This will run on application startup and show example usage
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileComparisonExample implements CommandLineRunner {

    private final FileComparisonService fileComparisonService;

    @Override
    public void run(String... args) throws Exception {
        // Only run the example if explicitly requested
        if (args.length > 0 && "demo".equals(args[0])) {
            runExample();
        }
    }

    private void runExample() {
        try {
            log.info("=== File Comparison Service Example ===");

            // Example 1: Get all files in monitored folder
            log.info("1. Getting all files in monitored folder...");
            List<String> allFiles = fileComparisonService.getAllFilesInMonitoredFolder();
            log.info("Found {} files: {}", allFiles.size(), allFiles);

            // Example 2: Compare with a supplied list
            log.info("2. Comparing with supplied list...");
            List<String> suppliedList = Arrays.asList("file1.txt", "file2.txt", "nonexistent.txt");
            FileComparisonResult result = fileComparisonService.compareFiles(suppliedList);
            
            log.info("Comparison results:");
            log.info("  - New files (in folder, not in list): {}", result.getNewFiles());
            log.info("  - Missing files (in list, not in folder): {}", result.getMissingFiles());
            log.info("  - Common files (in both): {}", result.getCommonFiles());

            // Example 3: Get only new files
            log.info("3. Getting only new files...");
            List<String> newFiles = fileComparisonService.getNewFiles(suppliedList);
            log.info("New files: {}", newFiles);

            // Example 4: Get only missing files
            log.info("4. Getting only missing files...");
            List<String> missingFiles = fileComparisonService.getMissingFiles(suppliedList);
            log.info("Missing files: {}", missingFiles);

            log.info("=== Example completed ===");

        } catch (Exception e) {
            log.error("Error running file comparison example: {}", e.getMessage(), e);
        }
    }
}
