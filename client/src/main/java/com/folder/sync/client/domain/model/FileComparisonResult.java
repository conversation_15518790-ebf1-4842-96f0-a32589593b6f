package com.folder.sync.client.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileComparisonResult {
    /**
     * Files that exist in the monitored folder but are not in the supplied list
     */
    private List<String> newFiles;
    
    /**
     * Files that are in the supplied list but do not exist in the monitored folder
     */
    private List<String> missingFiles;
    
    /**
     * Files that exist in both the monitored folder and the supplied list
     */
    private List<String> commonFiles;
}
