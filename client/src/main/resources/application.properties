spring.application.name=folder-sync-client

# WebSocket configuration
websocket.url=ws://localhost:8090/app

# File processing configuration
file.input-dir=${FILE_INPUT_DIR:/tmp/folder_sync_input}
file.max-file-size=10MB

# Folder monitoring configuration
monitor.folder=${file.input-dir}
monitor.retry.max-attempts=3
monitor.retry.delay-ms=1000
monitor.stability-check.max-attempts=5
monitor.stability-check.delay-ms=500

# Logging configuration
logging.level.root=INFO
logging.level.com.folder.sync.client=DEBUG

# Options: Exams options should be one of :- BECE_OMR, BECE_EMS, NCEE, GIFTED, SSCE_OMR, SSCE_EMS, NOV_OMR, NOV_EMS
system.name=SYS01
current.exams=BECE_OMR
