# Automatic Sync Trigger Implementation

This document explains the automatic file synchronization trigger that activates when the client connects to the server.

## 🔄 **Workflow Overview**

1. **<PERSON>lient connects** to server via WebSocket
2. **Server automatically sends** a list of files it already has
3. **Client receives** the file list message
4. **<PERSON><PERSON> automatically triggers** sync process for new files
5. **Only new files** (not in server's list) are processed and sent

## 📋 **Implementation Details**

### **Server Side**

#### 1. **ServerFileListMessage Model**
```java
public class ServerFileListMessage {
    private List<String> fileNames;        // Files server already has
    private String messageType = "FILE_LIST";
    private String message;                // Optional info message
}
```

#### 2. **Enhanced FileDataService**
- `getExistingFileNames()` - Returns list of all files server has received
- `getFileCount()` - Returns total count of files

#### 3. **WebSocket Connection Handler**
```java
@EventListener
public void handleWebSocketConnectListener(SessionConnectedEvent event) {
    // Get existing files from database
    List<String> existingFiles = fileDataService.getExistingFileNames();
    
    // Send to client via /topic/filelist
    ServerFileListMessage message = new ServerFileListMessage(existingFiles);
    messagingTemplate.convertAndSend("/topic/filelist", message);
}
```

### **Client Side**

#### 1. **Enhanced WebSocket Client**
- Automatically subscribes to `/topic/filelist` on connection
- Publishes received messages as Spring events

#### 2. **AutoSyncService**
```java
@EventListener
public void handleServerFileListMessage(ServerFileListMessage message) {
    // Automatically trigger sync for new files
    List<String> syncedFiles = fileSyncService.syncNewFilesToServer(
        message.getFileNames()
    );
}
```

## 🌐 **Message Flow**

```
Client                          Server
  |                               |
  |-------- WebSocket Connect --->|
  |                               |
  |                               |-- Get existing files from DB
  |                               |-- Create ServerFileListMessage
  |                               |
  |<-- /topic/filelist message ---|
  |                               |
  |-- AutoSyncService triggered --|
  |-- Compare local vs server ----|
  |-- Process new files ----------|
  |                               |
  |-------- Send new files ------>|
  |                               |-- Save to DB & disk
```

## 🔧 **Configuration**

### **WebSocket Topics**
- **File List**: `/topic/filelist` - Server sends file list to clients
- **File Sync**: `/app/sync` - Client sends files to server

### **Application Properties**
```properties
# WebSocket configuration
websocket.url=ws://localhost:8090/app

# Folder monitoring
monitor.folder=${file.input-dir}
```

## 📝 **Usage Examples**

### **Automatic Trigger (Default Behavior)**
```java
// Simply connect to server - sync happens automatically
clientSessionHandler.connect();
// Server sends file list → Client auto-syncs new files
```

### **Manual Trigger (Still Available)**
```java
// Manual sync if needed
List<String> knownFiles = Arrays.asList("file1.txt", "file2.txt");
List<String> synced = fileSyncService.syncNewFilesToServer(knownFiles);
```

### **REST API (Still Available)**
```bash
# Manual sync via REST
curl -X POST http://localhost:8080/api/sync/new \
  -H "Content-Type: application/json" \
  -d '["file1.txt", "file2.txt"]'
```

## 🧪 **Testing**

### **AutoSyncService Test**
```java
@Test
void testHandleServerFileListMessage_Success() {
    ServerFileListMessage message = new ServerFileListMessage(serverFiles);
    autoSyncService.handleServerFileListMessage(message);
    verify(fileSyncService).syncNewFilesToServer(serverFiles);
}
```

### **Integration Test Flow**
1. Start server
2. Connect client
3. Verify file list message sent
4. Verify auto-sync triggered
5. Verify new files processed and sent

## 🔍 **Monitoring & Logging**

### **Server Logs**
```
INFO - New WebSocket connection established, sending file list to client
INFO - Sent file list with 5 files to newly connected client
```

### **Client Logs**
```
INFO - Received file list from server with 5 files
INFO - Auto-sync completed: successfully synced 3 new files: [file6.txt, file7.txt, file8.txt]
INFO - No new files to sync - client and server are in sync
```

## ⚡ **Benefits**

1. **Zero Configuration** - Works automatically on connection
2. **Efficient** - Only syncs files that are actually new
3. **Reliable** - Uses existing WebSocket infrastructure
4. **Backwards Compatible** - Manual sync methods still work
5. **Event-Driven** - Uses Spring's event system for loose coupling

## 🔧 **Error Handling**

- **Connection Failures**: Automatic reconnection with retry logic
- **Sync Errors**: Logged but don't break the connection
- **File Processing Errors**: Individual file failures don't stop batch sync
- **Database Errors**: Gracefully handled with appropriate logging

## 🚀 **Future Enhancements**

- **Incremental Sync**: Only sync files modified since last sync
- **Bidirectional Sync**: Server can also request files from client
- **Conflict Resolution**: Handle files modified on both sides
- **Compression**: Compress file content for large files
