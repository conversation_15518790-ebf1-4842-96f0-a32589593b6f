# Java Folder Sync

A real-time file synchronization system built with Java and WebSockets with user-specific file management and session tracking.

## Features

- **Real-time file synchronization** between client and server
- **User-specific file storage** with dedicated folders for each user
- **Session management** with user authentication via WebSocket headers
- **Personalized file lists** sent to individual users
- **WebSocket-based communication** with STOMP protocol
- **Configurable monitoring** of input directories
- **Automatic retry mechanism** for failed transfers
- **File size limitations** and stability checks
- **Database persistence** with H2 in-memory database
- **RESTful API** for file statistics and user management

## Architecture

### User Session Management
- Each client connection is associated with a username via WebSocket headers
- Server creates dedicated directories for each user
- Session tracking with connection/disconnection events
- User activity monitoring and session cleanup

### File Storage
- User-specific directories: `/output-dir/{username}/`
- Files are stored with unique identifiers and timestamps
- Database records include user ownership information
- Personalized file lists sent to each user

### Communication
- **WebSocket Endpoint**: `/app` with STOMP protocol
- **User Authentication**: Via `user` header in connection
- **File Sync**: `/app/sync` for file uploads
- **File Lists**: `/user/queue/filelist` for personalized lists

## Prerequisites

- Java 21 or higher
- Maven 3.6.3 or higher

## Configuration

### Server Configuration (`server/src/main/resources/application.yml`)

```yaml
server:
  port: 8090

file:
  output-dir: ${FILE_OUTPUT_DIR:/tmp/folder_sync_output}
  max-file-size: 1000000000

spring:
  datasource:
    url: jdbc:h2:mem:folder_sync
    driverClassName: org.h2.Driver
```

### Client Configuration (`client/src/main/resources/application.properties`)

```properties
# WebSocket configuration
websocket.url=ws://localhost:8090/app

# System identification (used as username)
system.name=SYS01

# File processing configuration
file.input-dir=${FILE_INPUT_DIR:/tmp/folder_sync_input}
file.max-file-size=10MB

# Folder monitoring
monitor.folder=${file.input-dir}
```

### Environment Variables

- `FILE_OUTPUT_DIR`: Server output directory (default: `/tmp/folder_sync_output`)
- `FILE_INPUT_DIR`: Client input directory (default: `/tmp/folder_sync_input`)

## Building

```bash
# Build both client and server
mvn clean install

# Build server only
cd server
mvn clean install

# Build client only
cd client
mvn clean install
```

## Running

### Server
```bash
cd server
mvn spring-boot:run
```
The server will start on port 8090 and create user directories as clients connect.

### Client
```bash
cd client
mvn spring-boot:run
```
The client will connect using the `system.name` as the username and monitor the configured input directory.

## API Endpoints

### File Sync Statistics
- `GET /api/v1/file-sync/stats` - Overall server statistics
- `GET /api/v1/file-sync/recent?limit=10` - Recent files (all users)

### User-Specific Endpoints
- `GET /api/v1/file-sync/user/{username}/stats` - User file statistics
- `GET /api/v1/file-sync/user/{username}/recent` - User's recent files
- `GET /api/v1/file-sync/user/{username}/files` - User's file list

### Session Monitoring Endpoints
- `GET /api/v1/file-sync/monitor/stats` - Current session statistics
- `GET /api/v1/file-sync/monitor/sessions` - List of active sessions
- `POST /api/v1/file-sync/monitor/broadcast/stats` - Broadcast session stats via WebSocket
- `POST /api/v1/file-sync/monitor/broadcast/sessions` - Broadcast session list via WebSocket

### Example API Usage
```bash
# Get stats for user SYS01
curl http://localhost:8090/api/v1/file-sync/user/SYS01/stats

# Get recent files for user SYS01
curl http://localhost:8090/api/v1/file-sync/user/SYS01/recent

# Get all files for user SYS01
curl http://localhost:8090/api/v1/file-sync/user/SYS01/files

# Get session monitoring stats
curl http://localhost:8090/api/v1/file-sync/monitor/stats

# Get active sessions
curl http://localhost:8090/api/v1/file-sync/monitor/sessions

# Trigger session stats broadcast
curl -X POST http://localhost:8090/api/v1/file-sync/monitor/broadcast/stats
```

## User Session Management

### How It Works
1. **Client Connection**: Client connects with `user` header containing username
2. **Session Creation**: Server creates user session and dedicated directory
3. **File Processing**: Files are stored in user-specific directories
4. **Personalized Lists**: Each user receives only their own files
5. **Session Cleanup**: Sessions are marked inactive on disconnection

## Session Monitoring WebSocket

### Real-time Session Monitoring
The server provides a comprehensive WebSocket endpoint for monitoring user sessions in real-time.

### WebSocket Endpoints
- **Connection**: `/app` (same as file sync)
- **Monitoring Topic**: `/topic/session-monitor` (broadcasts to all monitoring clients)
- **Personal Queue**: `/user/queue/session-monitor` (direct responses to requests)

### Message Types

#### Request Messages (Client → Server)
- `/app/monitor/stats` - Request current session statistics
- `/app/monitor/sessions` - Request list of active sessions
- `/app/monitor/subscribe` - Subscribe to real-time updates with options

#### Response Messages (Server → Client)
- `SESSION_STATS` - Current session statistics
- `SESSION_LIST` - List of active sessions with details
- `SESSION_UPDATE` - Real-time session events (connect/disconnect/file upload)

### Monitoring Dashboard
Access the built-in monitoring dashboard at: `http://localhost:8090/session-monitor.html`

### Features
- **Real-time Statistics**: Active sessions, total users, files processed, average session duration
- **Session Details**: Username, session ID, connection time, last activity, files uploaded
- **Live Events**: Connection/disconnection events, file uploads, activity updates
- **Auto-refresh**: Periodic updates every 30 seconds for stats, 2 minutes for session list
- **Interactive Controls**: Manual refresh, connect/disconnect, event filtering

### Example WebSocket Client Usage
```javascript
// Connect to monitoring endpoint
const socket = new SockJS('/app');
const stompClient = Stomp.over(socket);

stompClient.connect({}, function (frame) {
    // Subscribe to monitoring broadcasts
    stompClient.subscribe('/topic/session-monitor', function (message) {
        const data = JSON.parse(message.body);
        handleMonitorMessage(data);
    });

    // Subscribe to personal responses
    stompClient.subscribe('/user/queue/session-monitor', function (message) {
        const data = JSON.parse(message.body);
        handleMonitorMessage(data);
    });

    // Request current stats
    stompClient.send("/app/monitor/stats", {}, {});

    // Request session list
    stompClient.send("/app/monitor/sessions", {}, {});
});
```

### Directory Structure
```
/tmp/folder_sync_output/
├── SYS01/                    # User SYS01's files
│   ├── uuid_file1.txt
│   └── uuid_file2.txt
├── SYS02/                    # User SYS02's files
│   ├── uuid_file3.txt
│   └── uuid_file4.txt
└── unknown/                  # Files from legacy clients
    └── uuid_legacy_file.txt
```

## Database Schema

### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE,
    username VARCHAR(255),
    connected_at TIMESTAMP,
    last_activity TIMESTAMP,
    is_active BOOLEAN
);
```

### File Data Table
```sql
CREATE TABLE file_data (
    id BIGINT PRIMARY KEY,
    original_file_path VARCHAR(255),
    content CLOB,
    file_size BIGINT,
    last_modified BIGINT,
    received_at TIMESTAMP,
    username VARCHAR(255)
);
```

## Testing

### Running Tests
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=UserSessionServiceTest

# Run server tests only
cd server && mvn test

# Run client tests only
cd client && mvn test
```

### Test Coverage
- **UserSessionService**: Session creation, management, and cleanup
- **FileDataService**: User-specific file operations and directory management
- **SessionMonitorService**: Real-time monitoring, statistics, and event broadcasting
- **WebSocket Integration**: Connection handling, user authentication, and monitoring

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Verify server is running on correct port (8090)
   - Check WebSocket URL in client configuration
   - Ensure firewall allows connections

2. **Files Not Syncing**
   - Verify input directory exists and has files
   - Check file permissions
   - Monitor logs for processing errors

3. **User Directory Not Created**
   - Check output directory permissions
   - Verify `FILE_OUTPUT_DIR` environment variable
   - Ensure username is being sent in connection header

### Logging
- **Server**: Logs user connections, file processing, and session management
- **Client**: Logs file monitoring, WebSocket connections, and sync operations
- **Log Level**: Set to DEBUG for detailed troubleshooting

## Development

### Key Components

#### Server
- `UserSessionService`: Manages user sessions and authentication
- `FileDataService`: Handles user-specific file storage and retrieval
- `SessionMonitorService`: Real-time session monitoring and event broadcasting
- `FileSyncWebSocketController`: WebSocket message handling and user management
- `SessionMonitorController`: WebSocket endpoint for session monitoring
- `WebSocketConfig`: User authentication and session setup
- `SchedulingConfig`: Periodic session statistics broadcasting

#### Client
- `ClientSessionHandler`: WebSocket connection and user identification
- `FileSyncService`: File processing and server communication
- `FolderMonitorService`: Directory monitoring and change detection

### Adding New Features
1. Update database schema if needed
2. Add service layer methods
3. Update WebSocket controllers
4. Add corresponding tests
5. Update API documentation

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
