# File Monitor Windows Service

This project implements a Windows service using Java and Spring Boot to monitor a specified folder for new files, decode their contents (e.g., JSON), and send the data to a remote server via WebSocket. The service runs autonomously, handles files that may be opened by other programs, and logs all activities for debugging and monitoring.

## Table of Contents
- [Project Overview](#project-overview)
- [Requirements](#requirements)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Handling Files in Use](#handling-files-in-use)
- [Setup Instructions](#setup-instructions)
- [Project Structure](#project-structure)
- [Configuration](#configuration)
- [Running as a Windows Service](#running-as-a-windows-service)
- [Testing](#testing)
- [Error Handling](#error-handling)
- [Deployment](#deployment)
- [Maintenance](#maintenance)
- [Assumptions](#assumptions)

## Project Overview
The File Monitor Windows Service:
- Monitors a configured folder for new file creation events.
- Decodes file contents (assumed to be JSON; customizable for other formats).
- Sends decoded data to a remote WebSocket server.
- Runs as a Windows service for continuous, autonomous operation.
- Handles files that may be opened or locked by other programs through retry logic and stability checks.

## Requirements
### Functional
- Detect file creation events in a specified directory.
- Read and decode file contents (e.g., JSON, CSV, or plain text).
- Send decoded data to a remote WebSocket server.
- Handle files that are temporarily locked or being written by other programs.
- Log all activities and errors for debugging.
### Non-Functional
- Run as a Windows service on Windows OS.
- Reliable WebSocket communication with reconnection logic.
- Configurable folder path and WebSocket URL.
- Scalable for concurrent file processing.
- Minimal resource usage.

## Architecture
The application is built using Spring Boot with the following components:
- **Folder Monitoring**: Uses `java.nio.file.WatchService` to detect new files.
- **File Processing**: Reads and decodes files using Jackson (for JSON) or other libraries, with retry logic for files in use.
- **WebSocket Client**: Uses Spring's `WebSocketClient` to send data to a remote server with reconnection handling.
- **Windows Service**: Runs the Spring Boot JAR as a service using Apache Commons Daemon (`procrun`).
- **Logging**: Uses SLF4J with Logback for detailed logging.

**Flow**:
1. Service starts and monitors the configured folder.
2. On new file detection, the file is read and decoded, with retries if the file is locked.
3. Decoded data is sent via WebSocket to the remote server.
4. Errors are logged, and WebSocket reconnections are handled.

## Technology Stack
- **Language**: Java 17
- **Framework**: Spring Boot 3.1.5
- **Libraries**:
  - `spring-boot-starter-websocket`: WebSocket client
  - `jackson-databind`: JSON parsing
  - `opencsv`: CSV parsing (optional)
  - `slf4j` and `logback-classic`: Logging
- **Windows Service**: Apache Commons Daemon (`procrun`)
- **Build Tool**: Maven

## Handling Files in Use
The application cannot directly detect if a file is opened by another program but infers this through:
- **Retry Logic**: Attempts to read the file up to 5 times with a 2-second delay if an `IOException` occurs (e.g., file locked by another process).
- **File Stability Check**: Verifies that the file size remains unchanged for 3 consecutive checks to ensure the file is no longer being written.
- **Asynchronous Processing**: Processes files in a separate thread to avoid blocking the folder monitoring thread.

These mechanisms ensure robust handling of files that are temporarily locked or being written by other programs (e.g., text editors, Excel).

## Setup Instructions
### Prerequisites
- Java 17 JDK
- Maven 3.8.x or higher
- Apache Commons Daemon (`prunsrv.exe`) for Windows service
- Windows OS for deployment

### Project Setup
1. **Clone the Repository**:
   ```bash
   git clone <repository-url>
   cd filemonitor
   ```

2. **Install Dependencies**:
   ```bash
   mvn clean install
   ```

3. **Configure Application**:
   Edit `src/main/resources/application.yml` to set:
   - `monitor.folder`: Path to the monitored folder (e.g., `C:/monitored_folder`).
   - `websocket.url`: WebSocket server URL (e.g., `ws://remote-server:8080`).

### Sample Code
#### Main Application (`FileMonitorApplication.java`)
```java
package com.example.filemonitor;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.PostConstruct;
import java.nio.file.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootApplication
public class FileMonitorApplication {

    private static final Logger logger = LoggerFactory.getLogger(FileMonitorApplication.class);

    @Value("${monitor.folder}")
    private String folderPath;

    @Value("${websocket.url}")
    private String webSocketUrl;

    public static void main(String[] args) {
        SpringApplication.run(FileMonitorApplication.class, args);
    }

    @Bean
    public ExecutorService executorService() {
        return Executors.newFixedThreadPool(2);
    }

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    @PostConstruct
    public void startMonitoring() {
        executorService().submit(this::monitorFolder);
    }

    private void monitorFolder() {
        try {
            WatchService watchService = FileSystems.getDefault().newWatchService();
            Path path = Paths.get(folderPath);
            path.register(watchService, StandardWatchEventKinds.ENTRY_CREATE);

            logger.info("Monitoring folder: {}", folderPath);

            while (true) {
                WatchKey key = watchService.take();
                for (WatchEvent<?> event : key.pollEvents()) {
                    if (event.kind() == StandardWatchEventKinds.ENTRY_CREATE) {
                        Path filePath = path.resolve((Path) event.context());
                        logger.info("New file detected: {}", filePath);
                        executorService().submit(() -> processFile(filePath));
                    }
                }
                key.reset();
            }
        } catch (Exception e) {
            logger.error("Error in folder monitoring", e);
        }
    }

    private void processFile(Path filePath) {
        int maxRetries = 5;
        int retryDelayMs = 2000;
        long lastSize = -1;
        int stableCount = 0;
        int maxStableChecks = 3;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                long currentSize = Files.size(filePath);
                if (lastSize == currentSize) {
                    stableCount++;
                } else {
                    stableCount = 0;
                    lastSize = currentSize;
                }

                if (stableCount < maxStableChecks) {
                    logger.debug("File {} size not stable (size: {}), waiting...", filePath, currentSize);
                    Thread.sleep(retryDelayMs);
                    continue;
                }

                String content = new String(Files.readAllBytes(filePath));
                ObjectMapper mapper = objectMapper();
                Object json = mapper.readValue(content, Object.class);
                String decodedData = mapper.writeValueAsString(json);
                sendToWebSocket(decodedData);
                logger.info("Successfully processed file: {}", filePath);
                return;
            } catch (IOException e) {
                logger.warn("Attempt {}/{} failed to read file {}: {}", attempt, maxRetries, filePath, e.getMessage());
                if (attempt == maxRetries) {
                    logger.error("Failed to process file {} after {} attempts", filePath, maxRetries, e);
                    return;
                }
                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException ie) {
                    logger.error("Interrupted while waiting to retry file {}", filePath, ie);
                    Thread.currentThread().interrupt();
                    return;
                }
            } catch (Exception e) {
                logger.error("Error processing file {}: {}", filePath, e.getMessage());
                return;
            }
        }
    }

    private void sendToWebSocket(String data) {
        int retries = 3;
        while (retries > 0) {
            try {
                StandardWebSocketClient client = new StandardWebSocketClient();
                WebSocketSession session = client.doHandshake(new WebSocketHandler() {
                    @Override
                    public void handleMessage(WebSocketSession session, TextMessage message) {
                        logger.info("Received from server: {}", message.getPayload());
                    }
                }, webSocketUrl).get();
                session.sendMessage(new TextMessage(data));
                logger.info("Sent data to WebSocket: {}", data);
                session.close();
                return;
            } catch (Exception e) {
                logger.error("WebSocket retry {}/3 failed", 4 - retries, e);
                retries--;
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ie) {
                    logger.error("Interrupted during WebSocket retry", ie);
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }
        logger.error("Failed to send data after retries");
    }
}
```

#### Configuration (`application.yml`)
```yaml
monitor:
  folder: C:/monitored_folder
websocket:
  url: ws://remote-server:8080
logging:
  level:
    root: INFO
    com.example.filemonitor: DEBUG
  file:
    name: logs/filemonitor.log
```

#### Maven Configuration (`pom.xml`)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example</groupId>
    <artifactId>filemonitor</artifactId>
    <version>1.0.0</version>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.5</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.7.1</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.11</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

## Project Structure
```
filemonitor/
├── src/
│   ├── main/
│   │   ├── java/com/example/filemonitor/
│   │   │   └── FileMonitorApplication.java
│   │   ├── resources/
│   │   │   └── application.yml
│   └── test/
├── pom.xml
├── logs/
│   └── filemonitor.log
└── README.md
```

## Configuration
- **Folder Path**: Set `monitor.folder` in `application.yml` to the directory to monitor (e.g., `C:/monitored_folder`).
- **WebSocket URL**: Set `websocket.url` to the remote server's WebSocket endpoint.
- **Logging**: Logs are written to `logs/filemonitor.log`. Adjust `logging.level` for verbosity.

## Running as a Windows Service
### Prerequisites
- Download Apache Commons Daemon (`prunsrv.exe`) from [Apache Commons](https://commons.apache.org/proper/commons-daemon/).
- Place `prunsrv.exe` in `C:/procrun`.

### Build the Application
```bash
mvn clean package
```

### Install the Service
```bash
C:\procrun\prunsrv.exe //IS//FileMonitorService ^
  --DisplayName="File Monitor Service" ^
  --Startup=auto ^
  --StartMode=jvm ^
  --StartClass=com.example.filemonitor.FileMonitorApplication ^
  --StartMethod=main ^
  --Classpath=C:\path\to\filemonitor-1.0.0.jar ^
  --Jvm=C:\Program Files\Java\jdk-17\bin\server\jvm.dll ^
  --LogPath=C:\logs ^
  --LogLevel=Debug
```

### Start the Service
```bash
net start FileMonitorService
```

### Verify Operation
- Check logs at `C:/logs/filemonitor.log` for file detection, processing, and WebSocket activity.
- Add test files to the monitored folder and verify WebSocket messages.
- Test with files opened by other programs (e.g., Notepad) to ensure retry logic works.

## Testing
### Unit Tests
- Test file decoding with JUnit and mock files.
- Test WebSocket sending with a mock server (e.g., `MockWebServer`).

### Integration Tests
- Simulate file creation and verify WebSocket transmission.
- Test with files locked by other programs to validate retry logic.
- Test service start/stop using `net start/stop`.

### Stress Tests
- Add multiple files simultaneously to test concurrency.
- Simulate WebSocket server downtime to test reconnection.

## Error Handling
- **File Processing**:
  - Retries file access up to 5 times with 2-second delays if locked.
  - Checks file size stability to ensure the file is not being written.
  - Validates file types and handles large files with streaming.
- **WebSocket**:
  - Implements reconnection logic with 3 retries and 5-second delays.
- **Logging**:
  - Uses Logback for log rotation and detailed output.
  - Logs retry attempts, file stability, and errors.

## Deployment
1. Ensure Java 17 is installed on the target machine.
2. Copy the JAR and `prunsrv.exe` to the server.
3. Update `application.yml` with correct paths and URLs.
4. Install and start the service as described above.

## Maintenance
- **Monitoring**: Check logs and Windows Event Viewer for issues.
- **Updates**: Replace the JAR and restart the service for updates.
- **Scaling**: Increase thread pool size in `executorService` for high file volumes.
- **Tuning**: Adjust `maxRetries`, `retryDelayMs`, and `maxStableChecks` in `processFile` based on file write patterns.

## Assumptions
- Files are JSON-based; modify `processFile` for other formats (e.g., CSV with OpenCSV).
- WebSocket server accepts text messages; add authentication if needed.
- Monitored folder is `C:/monitored_folder`; configurable in `application.yml`.
- No specific security requirements (e.g., file encryption, WebSocket authentication).
- Files may be temporarily locked by other programs, handled via retries and stability checks.

## Contributing
Submit issues or pull requests to the repository for enhancements or bug fixes.

## License
MIT License