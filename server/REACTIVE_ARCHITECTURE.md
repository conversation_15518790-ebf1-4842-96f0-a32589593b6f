# Reactive Architecture Implementation

## Overview

The Folder Sync Server has been enhanced with reactive programming capabilities using Spring WebFlux and R2DBC. This implementation provides non-blocking, asynchronous processing for improved scalability and performance.

## Key Components

### Reactive Dependencies Added

- **Spring WebFlux**: Reactive web framework
- **Spring Data R2DBC**: Reactive database access
- **R2DBC H2**: Reactive H2 database driver
- **Reactor Test**: Testing utilities for reactive streams

### Reactive Data Layer

#### Reactive Models
- `ReactiveFileData`: R2DBC-compatible file data entity
- `ReactiveUserSession`: R2DBC-compatible user session entity

#### Reactive Repositories
- `ReactiveFileDataRepository`: Reactive CRUD operations for file data
- `ReactiveUserSessionRepository`: Reactive CRUD operations for user sessions

### Reactive Service Layer

#### ReactiveFileDataService
- Non-blocking file processing
- Reactive file I/O operations
- Reactive database operations
- Returns `Mono<T>` and `Flux<T>` types

#### ReactiveUserSessionService
- Reactive session management
- Non-blocking session creation/updates
- Reactive session cleanup

#### ReactiveSessionMonitorService
- Reactive session monitoring
- Periodic reactive streams for broadcasting
- Non-blocking statistics generation

### Reactive Controllers

#### ReactiveFileSyncWebSocketController
- Reactive WebSocket message handling
- Non-blocking file processing
- Reactive session management integration

#### ReactiveSessionMonitorController
- Reactive session monitoring endpoints
- Non-blocking statistics delivery

#### ReactiveFileSyncController
- RESTful reactive endpoints
- Server-Sent Events (SSE) for real-time updates
- Reactive data streaming

### Configuration

#### R2dbcConfig
- R2DBC connection factory configuration
- Database initialization
- Repository scanning

#### ReactiveSchedulingConfig
- Reactive stream scheduling
- Replaces traditional `@Scheduled` annotations
- Manages periodic reactive streams

## Reactive Features

### Non-Blocking Operations
- All database operations are non-blocking
- File I/O operations use reactive wrappers
- WebSocket message processing is reactive

### Backpressure Handling
- Reactive streams handle backpressure automatically
- Prevents memory overflow under high load
- Graceful degradation under pressure

### Real-Time Streaming
- Server-Sent Events for real-time updates
- Reactive WebSocket handling
- Live session monitoring

### Error Handling
- Reactive error handling with `doOnError`
- Graceful error recovery
- Continued operation after errors

## API Endpoints

### Reactive REST Endpoints
- `GET /api/reactive/files` - Stream all files
- `GET /api/reactive/files/count` - Get file count
- `GET /api/reactive/user/{username}/files` - Stream user files
- `GET /api/reactive/monitor/stats` - Get session statistics
- `GET /api/reactive/monitor/sessions` - Stream session information

### Server-Sent Events (SSE)
- `GET /api/reactive/monitor/stats/stream` - Real-time session stats
- `GET /api/reactive/monitor/sessions/stream` - Real-time session list
- `GET /api/reactive/files/count/stream` - Real-time file count updates

### WebSocket Endpoints
- `/app/file-sync` - Reactive file synchronization
- `/app/connect` - Reactive connection handling
- `/app/disconnect` - Reactive disconnection handling
- `/app/monitor/stats` - Reactive session statistics
- `/app/monitor/sessions` - Reactive session monitoring

## Benefits

### Performance
- Non-blocking I/O improves throughput
- Better resource utilization
- Reduced thread pool requirements

### Scalability
- Handles more concurrent connections
- Lower memory footprint per connection
- Better performance under load

### Responsiveness
- Real-time updates via reactive streams
- Immediate feedback to clients
- Reduced latency

### Resilience
- Better error handling and recovery
- Graceful degradation under load
- Automatic backpressure management

## Testing

### Reactive Testing
- Uses `StepVerifier` for testing reactive streams
- Verifies asynchronous behavior
- Tests backpressure scenarios

### Example Test
```java
StepVerifier.create(fileDataService.getFileCount())
    .expectNext(5L)
    .verifyComplete();
```

## Migration Strategy

The reactive implementation runs alongside the traditional implementation:

1. **Gradual Migration**: Both reactive and traditional services coexist
2. **Feature Parity**: Reactive services provide same functionality
3. **Testing**: Comprehensive testing of reactive components
4. **Monitoring**: Performance comparison between implementations

## Configuration

### Application Properties
```yaml
spring:
  r2dbc:
    url: r2dbc:h2:mem:///folder_sync
    username: sa
    password:
```

### Database Schema
The same database schema is used for both implementations, ensuring compatibility.

## Future Enhancements

1. **Full Migration**: Complete migration to reactive architecture
2. **Reactive Security**: Implement reactive security features
3. **Reactive Caching**: Add reactive caching layer
4. **Metrics**: Reactive metrics and monitoring
5. **Load Testing**: Performance testing under various loads
