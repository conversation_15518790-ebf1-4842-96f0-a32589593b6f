spring:
  application:
    name: folder-sync-server
  profiles:
    active: traditional  # Default to traditional implementation
  datasource:
    url: jdbc:h2:mem:folder_sync
    driverClassName: org.h2.Driver
    username: sa
    password:
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: update
  r2dbc:
    url: r2dbc:h2:mem:///folder_sync
    username: sa
    password:

server:
  port: 8090

file:
  output-dir: ${FILE_OUTPUT_DIR:/tmp/folder_sync_output}
  max-file-size: 1000000000

monitor:
  folder: /path/to/monitor
  retry:
    max-attempts: 3
    delay-ms: 1000
  stability-check:
    max-attempts: 5
    delay-ms: 500
