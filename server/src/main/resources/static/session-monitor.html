<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Monitor</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.6.1/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #1976d2;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .session-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .session-table th, .session-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .session-table th {
            background-color: #f2f2f2;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }
        .status.connected {
            background-color: #4caf50;
            color: white;
        }
        .status.disconnected {
            background-color: #f44336;
            color: white;
        }
        .events-log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #fafafa;
            font-family: monospace;
            font-size: 0.9em;
        }
        .event {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #2196f3;
            background: white;
        }
        .event.connected {
            border-left-color: #4caf50;
        }
        .event.disconnected {
            border-left-color: #f44336;
        }
        .event.file-uploaded {
            border-left-color: #ff9800;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .connection-status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .connection-status.connected {
            background: #d4edda;
            color: #155724;
        }
        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Session Monitor Dashboard</h1>
        
        <div class="card">
            <h2>Connection Status</h2>
            <div id="connectionStatus" class="connection-status disconnected">
                Disconnected
            </div>
            <div class="controls">
                <button id="connectBtn" onclick="connect()">Connect</button>
                <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
                <button onclick="requestStats()">Refresh Stats</button>
                <button onclick="requestSessions()">Refresh Sessions</button>
            </div>
        </div>

        <div class="card">
            <h2>📈 Session Statistics</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="activeSessions">-</div>
                    <div class="stat-label">Active Sessions</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalUsers">-</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalFiles">-</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgDuration">-</div>
                    <div class="stat-label">Avg Duration (min)</div>
                </div>
            </div>
            <div>
                <strong>Last Update:</strong> <span id="lastUpdate">-</span>
            </div>
        </div>

        <div class="card">
            <h2>👥 Active Sessions</h2>
            <table class="session-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Session ID</th>
                        <th>Connected At</th>
                        <th>Last Activity</th>
                        <th>Duration (min)</th>
                        <th>Files Uploaded</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="sessionsTableBody">
                    <tr>
                        <td colspan="7" style="text-align: center; color: #666;">No active sessions</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <h2>📝 Real-time Events</h2>
            <div class="events-log" id="eventsLog">
                <div>Waiting for events...</div>
            </div>
            <button onclick="clearEvents()">Clear Events</button>
        </div>
    </div>

    <script>
        let stompClient = null;
        let connected = false;

        function connect() {
            const socket = new SockJS('/app');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({}, function (frame) {
                console.log('Connected: ' + frame);
                connected = true;
                updateConnectionStatus();
                
                // Subscribe to session monitoring topic
                stompClient.subscribe('/topic/session-monitor', function (message) {
                    const data = JSON.parse(message.body);
                    handleMonitorMessage(data);
                });
                
                // Subscribe to personal queue for direct responses
                stompClient.subscribe('/user/queue/session-monitor', function (message) {
                    const data = JSON.parse(message.body);
                    handleMonitorMessage(data);
                });
                
                // Request initial data
                requestStats();
                requestSessions();
                
                addEvent('System', 'Monitor connected to server', 'connected');
            }, function (error) {
                console.log('Connection error: ' + error);
                connected = false;
                updateConnectionStatus();
                addEvent('System', 'Connection failed: ' + error, 'disconnected');
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
                connected = false;
                updateConnectionStatus();
                addEvent('System', 'Monitor disconnected', 'disconnected');
            }
        }

        function updateConnectionStatus() {
            const statusDiv = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'connection-status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'connection-status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function requestStats() {
            if (stompClient && connected) {
                stompClient.send("/app/monitor/stats", {}, {});
            }
        }

        function requestSessions() {
            if (stompClient && connected) {
                stompClient.send("/app/monitor/sessions", {}, {});
            }
        }

        function handleMonitorMessage(data) {
            console.log('Received monitor message:', data);
            
            if (data.type === 'SESSION_STATS' && data.stats) {
                updateStats(data.stats);
            } else if (data.type === 'SESSION_LIST' && data.activeSessions) {
                updateSessionsTable(data.activeSessions);
                if (data.stats) {
                    updateStats(data.stats);
                }
            } else if (data.type === 'SESSION_UPDATE' && data.event) {
                handleSessionEvent(data.event);
            }
        }

        function updateStats(stats) {
            document.getElementById('activeSessions').textContent = stats.totalActiveSessions;
            document.getElementById('totalUsers').textContent = stats.totalUsers;
            document.getElementById('totalFiles').textContent = stats.totalFilesProcessed;
            document.getElementById('avgDuration').textContent = Math.round(stats.averageSessionDuration * 10) / 10;
            document.getElementById('lastUpdate').textContent = new Date(stats.lastUpdate).toLocaleString();
        }

        function updateSessionsTable(sessions) {
            const tbody = document.getElementById('sessionsTableBody');
            
            if (sessions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #666;">No active sessions</td></tr>';
                return;
            }
            
            tbody.innerHTML = sessions.map(session => `
                <tr>
                    <td>${session.username}</td>
                    <td>${session.sessionId.substring(0, 8)}...</td>
                    <td>${new Date(session.connectedAt).toLocaleString()}</td>
                    <td>${new Date(session.lastActivity).toLocaleString()}</td>
                    <td>${session.sessionDurationMinutes}</td>
                    <td>${session.filesUploaded}</td>
                    <td><span class="status ${session.active ? 'connected' : 'disconnected'}">${session.active ? 'Active' : 'Inactive'}</span></td>
                </tr>
            `).join('');
        }

        function handleSessionEvent(event) {
            addEvent(event.username, `${event.eventType}: ${event.details}`, event.eventType.toLowerCase().replace('_', '-'));
        }

        function addEvent(username, message, type) {
            const eventsLog = document.getElementById('eventsLog');
            const eventDiv = document.createElement('div');
            eventDiv.className = `event ${type}`;
            eventDiv.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}</strong> 
                [${username}] ${message}
            `;
            eventsLog.appendChild(eventDiv);
            eventsLog.scrollTop = eventsLog.scrollHeight;
        }

        function clearEvents() {
            document.getElementById('eventsLog').innerHTML = '<div>Events cleared...</div>';
        }

        // Auto-connect on page load
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html>
