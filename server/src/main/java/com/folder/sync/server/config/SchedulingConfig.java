package com.folder.sync.server.config;

import com.folder.sync.server.service.SessionMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@org.springframework.context.annotation.Profile("traditional")
@EnableScheduling
@Slf4j
public class SchedulingConfig {
    
    private final SessionMonitorService sessionMonitorService;
    
    @Autowired
    public SchedulingConfig(SessionMonitorService sessionMonitorService) {
        this.sessionMonitorService = sessionMonitorService;
    }
    
    /**
     * Periodically broadcast session statistics to monitoring clients
     * Runs every 30 seconds
     */
    @Scheduled(fixedRate = 30000)
    public void broadcastSessionStats() {
        try {
            sessionMonitorService.broadcastSessionStats();
        } catch (Exception e) {
            log.error("Error in scheduled session stats broadcast: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Periodically broadcast complete session list to monitoring clients
     * Runs every 2 minutes
     */
    @Scheduled(fixedRate = 120000)
    public void broadcastSessionList() {
        try {
            sessionMonitorService.broadcastSessionList();
        } catch (Exception e) {
            log.error("Error in scheduled session list broadcast: {}", e.getMessage(), e);
        }
    }
}
