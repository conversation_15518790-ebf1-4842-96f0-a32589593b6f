package com.folder.sync.server.domain.model;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Table("user_sessions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReactiveUserSession {
    
    @Id
    private Long id;
    
    @Column("session_id")
    private String sessionId;
    
    @Column("username")
    private String username;
    
    @Column("connected_at")
    private LocalDateTime connectedAt;
    
    @Column("last_activity")
    private LocalDateTime lastActivity;
    
    @Column("is_active")
    @Builder.Default
    private Boolean isActive = true;

    public ReactiveUserSession(String sessionId, String username) {
        this.sessionId = sessionId;
        this.username = username;
        this.connectedAt = LocalDateTime.now();
        this.lastActivity = LocalDateTime.now();
        this.isActive = true;
    }

    public void updateLastActivity() {
        this.lastActivity = LocalDateTime.now();
    }

    public void disconnect() {
        this.isActive = false;
        this.lastActivity = LocalDateTime.now();
    }
}
