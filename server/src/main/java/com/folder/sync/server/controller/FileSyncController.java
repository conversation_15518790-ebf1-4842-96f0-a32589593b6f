package com.folder.sync.server.controller;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.domain.repository.FileDataRepository;
import com.folder.sync.server.service.FileDataService;
import com.folder.sync.server.service.SessionMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/file-sync")
public class FileSyncController {

    private final FileDataRepository fileDataRepository;
    private final FileDataService fileDataService;
    private final SessionMonitorService sessionMonitorService;

    @Autowired
    public FileSyncController(FileDataRepository fileDataRepository,
                            FileDataService fileDataService,
                            SessionMonitorService sessionMonitorService) {
        this.fileDataRepository = fileDataRepository;
        this.fileDataService = fileDataService;
        this.sessionMonitorService = sessionMonitorService;
    }

    @GetMapping("/stats")
    public ResponseEntity<FileSyncStats> getStats() {
        long totalFiles = fileDataRepository.count();
        return ResponseEntity.ok(new FileSyncStats(totalFiles));
    }

    @GetMapping("/recent")
    public ResponseEntity<List<FileData>> getRecentFiles(@RequestParam(defaultValue = "10") int limit) {
        List<FileData> recentFiles = fileDataRepository.findTopByOrderByReceivedAtDesc(limit);
        return ResponseEntity.ok(recentFiles);
    }

    @GetMapping("/user/{username}/stats")
    public ResponseEntity<UserFileSyncStats> getUserStats(@PathVariable String username) {
        long totalFiles = fileDataService.getFileCountForUser(username);
        List<String> fileNames = fileDataService.getExistingFileNamesForUser(username);
        return ResponseEntity.ok(new UserFileSyncStats(username, totalFiles, fileNames));
    }

    @GetMapping("/user/{username}/recent")
    public ResponseEntity<List<FileData>> getUserRecentFiles(@PathVariable String username) {
        List<FileData> recentFiles = fileDataService.getRecentFilesForUser(username);
        return ResponseEntity.ok(recentFiles);
    }

    @GetMapping("/user/{username}/files")
    public ResponseEntity<List<String>> getUserFiles(@PathVariable String username) {
        List<String> userFiles = fileDataService.getExistingFileNamesForUser(username);
        return ResponseEntity.ok(userFiles);
    }

    @GetMapping("/monitor/stats")
    public ResponseEntity<SessionMonitorMessage.SessionStats> getSessionStats() {
        SessionMonitorMessage.SessionStats stats = sessionMonitorService.getCurrentStats();
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/monitor/sessions")
    public ResponseEntity<List<SessionMonitorMessage.SessionInfo>> getActiveSessions() {
        List<SessionMonitorMessage.SessionInfo> sessions = sessionMonitorService.getCurrentSessions();
        return ResponseEntity.ok(sessions);
    }

    @PostMapping("/monitor/broadcast/stats")
    public ResponseEntity<String> broadcastStats() {
        sessionMonitorService.broadcastSessionStats();
        return ResponseEntity.ok("Session stats broadcasted successfully");
    }

    @PostMapping("/monitor/broadcast/sessions")
    public ResponseEntity<String> broadcastSessions() {
        sessionMonitorService.broadcastSessionList();
        return ResponseEntity.ok("Session list broadcasted successfully");
    }
}

record FileSyncStats(long totalFiles) {}
record UserFileSyncStats(String username, long totalFiles, List<String> fileNames) {}
