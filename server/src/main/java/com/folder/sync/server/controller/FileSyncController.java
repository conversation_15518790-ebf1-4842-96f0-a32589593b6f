package com.folder.sync.server.controller;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.service.FileDataService;
import com.folder.sync.server.service.SessionMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;

@RestController
@RequestMapping("/api")
public class FileSyncController {

    private final FileDataService fileDataService;
    private final SessionMonitorService sessionMonitorService;

    @Autowired
    public FileSyncController(FileDataService fileDataService,
                                    SessionMonitorService sessionMonitorService) {
        this.fileDataService = fileDataService;
        this.sessionMonitorService = sessionMonitorService;
    }

    @GetMapping("/files")
    public Flux<String> getAllFiles() {
        return fileDataService.getExistingFileNames();
    }

    @GetMapping("/files/count")
    public Mono<Long> getFileCount() {
        return fileDataService.getFileCount();
    }

    @GetMapping("/user/{username}/files")
    public Flux<String> getUserFiles(@PathVariable String username) {
        return fileDataService.getExistingFileNamesForUser(username);
    }

    @GetMapping("/user/{username}/files/count")
    public Mono<Long> getUserFileCount(@PathVariable String username) {
        return fileDataService.getFileCountForUser(username);
    }

    @GetMapping("/user/{username}/recent")
    public Flux<FileData> getUserRecentFiles(@PathVariable String username) {
        return fileDataService.getRecentFilesForUser(username);
    }

    @GetMapping("/monitor/stats")
    public Mono<SessionMonitorMessage.SessionStats> getSessionStats() {
        return sessionMonitorService.getCurrentStats();
    }

    @GetMapping("/monitor/sessions")
    public Flux<SessionMonitorMessage.SessionInfo> getCurrentSessions() {
        return sessionMonitorService.getCurrentSessions();
    }

    /**
     * Server-Sent Events endpoint for real-time session stats
     */
    @GetMapping(value = "/monitor/stats/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<SessionMonitorMessage.SessionStats> getSessionStatsStream() {
        return Flux.interval(Duration.ofSeconds(5))
            .flatMap(tick -> sessionMonitorService.getCurrentStats())
            .distinctUntilChanged();
    }

    /**
     * Server-Sent Events endpoint for real-time session list
     */
    @GetMapping(value = "/monitor/sessions/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<SessionMonitorMessage.SessionInfo> getSessionsStream() {
        return Flux.interval(Duration.ofSeconds(10))
            .flatMap(tick -> sessionMonitorService.getCurrentSessions());
    }

    /**
     * Server-Sent Events endpoint for real-time file count updates
     */
    @GetMapping(value = "/files/count/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<Long> getFileCountStream() {
        return Flux.interval(Duration.ofSeconds(5))
            .flatMap(tick -> fileDataService.getFileCount())
            .distinctUntilChanged();
    }

    /**
     * Server-Sent Events endpoint for real-time user file count updates
     */
    @GetMapping(value = "/user/{username}/files/count/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<Long> getUserFileCountStream(@PathVariable String username) {
        return Flux.interval(Duration.ofSeconds(5))
            .flatMap(tick -> fileDataService.getFileCountForUser(username))
            .distinctUntilChanged();
    }
}
