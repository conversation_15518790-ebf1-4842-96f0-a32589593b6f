package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.model.FileSyncMessage;
import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.service.FileDataService;
import com.folder.sync.server.service.SessionMonitorService;
import com.folder.sync.server.service.UserSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.List;

@Controller
@Slf4j
public class FileSyncWebSocketController {

    private final FileDataService fileDataService;
    private final UserSessionService userSessionService;
    private final SessionMonitorService sessionMonitorService;
    private final SimpMessagingTemplate messagingTemplate;

    @Autowired
    public FileSyncWebSocketController(FileDataService fileDataService,
                                     UserSessionService userSessionService,
                                     SessionMonitorService sessionMonitorService,
                                     SimpMessagingTemplate messagingTemplate) {
        this.fileDataService = fileDataService;
        this.userSessionService = userSessionService;
        this.sessionMonitorService = sessionMonitorService;
        this.messagingTemplate = messagingTemplate;
    }

    @MessageMapping("/file-sync")
    @SendToUser("/queue/file-sync-response")
    public Mono<FileSyncMessage> handleFileSync(@Payload FileSyncMessage message,
                                               @Header("username") String username,
                                               SimpMessageHeaderAccessor headerAccessor) {
        
        String sessionId = headerAccessor.getSessionId();
        log.info("Received file sync message from user: {} with session: {}", username, sessionId);

        return userSessionService.updateSessionActivity(sessionId)
            .then(processFileSyncMessage(message, username, sessionId))
            .doOnSuccess(response -> log.info("Successfully processed file sync for user: {}", username))
            .doOnError(error -> log.error("Error processing file sync for user: {}: {}", username, error.getMessage()));
    }

    @MessageMapping("/connect")
    @SendToUser("/queue/connect-response")
    public Mono<FileSyncMessage> handleConnect(@Header("username") String username,
                                             SimpMessageHeaderAccessor headerAccessor) {
        
        String sessionId = headerAccessor.getSessionId();
        log.info("User {} connecting with session: {}", username, sessionId);

        return userSessionService.createSession(sessionId, username)
            .then(fileDataService.createUserDirectory(username))
            .then(getExistingFileList(username))
            .map(fileList -> {
                FileSyncMessage response = new FileSyncMessage();
                response.setType("FILE_LIST");
                response.setFileList(fileList);
                response.setMessage("Connected successfully. Here are your existing files.");
                return response;
            })
            .doOnSuccess(response -> log.info("User {} connected successfully", username))
            .doOnError(error -> log.error("Error connecting user {}: {}", username, error.getMessage()));
    }

    @MessageMapping("/monitor/stats")
    @SendToUser("/queue/monitor-stats")
    public Mono<SessionMonitorMessage.SessionStats> handleStatsRequest(@Header("username") String username,
                                                                       SimpMessageHeaderAccessor headerAccessor) {
        
        String sessionId = headerAccessor.getSessionId();
        log.debug("Stats request from user: {} with session: {}", username, sessionId);

        return userSessionService.updateSessionActivity(sessionId)
            .then(sessionMonitorService.getCurrentStats())
            .doOnSuccess(stats -> log.debug("Sent stats to user: {}", username))
            .doOnError(error -> log.error("Error getting stats for user: {}: {}", username, error.getMessage()));
    }

    private Mono<FileSyncMessage> processFileSyncMessage(FileSyncMessage message, String username, String sessionId) {
        return switch (message.getType()) {
            case "FILE_UPLOAD" -> handleFileUpload(message, username);
            case "FILE_LIST_REQUEST" -> handleFileListRequest(username);
            case "PING" -> handlePing();
            default -> Mono.just(createErrorResponse("Unknown message type: " + message.getType()));
        };
    }

    private Mono<FileSyncMessage> handleFileUpload(FileSyncMessage message, String username) {
        return fileDataService.processFileData(
                message.getFilePath(),
                message.getContent(),
                message.getFileSize(),
                message.getLastModified(),
                username
            )
            .map(savedFile -> {
                FileSyncMessage response = new FileSyncMessage();
                response.setType("FILE_UPLOAD_SUCCESS");
                response.setFilePath(savedFile.getOriginalFilePath());
                response.setMessage("File uploaded successfully");
                return response;
            })
            .onErrorReturn(createErrorResponse("Failed to upload file"));
    }

    private Mono<FileSyncMessage> handleFileListRequest(String username) {
        return getExistingFileList(username)
            .map(fileList -> {
                FileSyncMessage response = new FileSyncMessage();
                response.setType("FILE_LIST");
                response.setFileList(fileList);
                response.setMessage("File list retrieved successfully");
                return response;
            });
    }

    private Mono<FileSyncMessage> handlePing() {
        FileSyncMessage response = new FileSyncMessage();
        response.setType("PONG");
        response.setMessage("Server is alive");
        return Mono.just(response);
    }

    private Mono<List<String>> getExistingFileList(String username) {
        return fileDataService.getExistingFileNamesForUser(username)
            .collectList();
    }

    private FileSyncMessage createErrorResponse(String errorMessage) {
        FileSyncMessage response = new FileSyncMessage();
        response.setType("ERROR");
        response.setMessage(errorMessage);
        return response;
    }
}
