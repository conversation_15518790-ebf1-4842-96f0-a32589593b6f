package com.folder.sync.server.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

@Configuration
@EnableWebSocketMessageBroker
@Slf4j
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/app")
                .setAllowedOrigins("*")
                .withSockJS();

        registry.addEndpoint("/app")
                .setAllowedOrigins("*");
    }
    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // Enable a simple message broker to carry the messages back to the client on destinations prefixed with /topic and /queue
        registry.enableSimpleBroker("/topic", "/queue");
        // Set the application destination prefix for all messages mapped with @MessageMapping
        registry.setApplicationDestinationPrefixes("/app");
        // Enable user-specific destinations
        registry.setUserDestinationPrefix("/user");
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(new ChannelInterceptor() {
            @Override
            public Message<?> preSend(@NonNull Message<?> message, @NonNull MessageChannel channel) {
                StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

                if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
                    // Extract username from the "user" header sent by the client
                    String username = accessor.getFirstNativeHeader("user");

                    if (username != null && !username.trim().isEmpty()) {
                        log.info("Setting user principal for session: {} with username: {}",
                                accessor.getSessionId(), username);

                        // Create and set the user principal
                        UserPrincipal userPrincipal = new UserPrincipal(username.trim());
                        accessor.setUser(userPrincipal);
                    } else {
                        log.warn("No username found in connection headers for session: {}",
                                accessor.getSessionId());
                    }
                }

                return message;
            }
        });
    }
}