package com.folder.sync.server.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SessionMonitorMessage {
    private String type; // "SESSION_UPDATE", "SESSION_STATS", "SESSION_LIST"
    private LocalDateTime timestamp;
    private SessionStats stats;
    private List<SessionInfo> activeSessions;
    private SessionEvent event;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionStats {
        private long totalActiveSessions;
        private long totalUsers;
        private long totalFilesProcessed;
        private double averageSessionDuration;
        private LocalDateTime lastUpdate;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionInfo {
        private String sessionId;
        private String username;
        private LocalDateTime connectedAt;
        private LocalDateTime lastActivity;
        private boolean isActive;
        private long filesUploaded;
        private String ipAddress;
        private long sessionDurationMinutes;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionEvent {
        private String eventType; // "CONNECTED", "DISCONNECTED", "FILE_UPLOADED", "ACTIVITY_UPDATE"
        private String sessionId;
        private String username;
        private LocalDateTime timestamp;
        private String details;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubscriptionRequest {
        private boolean includeStats = true;
        private boolean includeSessions = true;
        private boolean includeEvents = true;
        private int updateIntervalSeconds = 5;
    }
    
    // Factory methods for different message types
    public static SessionMonitorMessage createStatsMessage(SessionStats stats) {
        SessionMonitorMessage message = new SessionMonitorMessage();
        message.setType("SESSION_STATS");
        message.setTimestamp(LocalDateTime.now());
        message.setStats(stats);
        return message;
    }
    
    public static SessionMonitorMessage createSessionListMessage(List<SessionInfo> sessions, SessionStats stats) {
        SessionMonitorMessage message = new SessionMonitorMessage();
        message.setType("SESSION_LIST");
        message.setTimestamp(LocalDateTime.now());
        message.setActiveSessions(sessions);
        message.setStats(stats);
        return message;
    }
    
    public static SessionMonitorMessage createEventMessage(SessionEvent event) {
        SessionMonitorMessage message = new SessionMonitorMessage();
        message.setType("SESSION_UPDATE");
        message.setTimestamp(LocalDateTime.now());
        message.setEvent(event);
        return message;
    }
}
