package com.folder.sync.server.domain.model;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Table("file_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReactiveFileData {
    
    @Id
    private Long id;

    @Column("original_file_path")
    private String originalFilePath;
    
    @Column("content")
    private String content;
    
    @Column("file_size")
    private long fileSize;
    
    @Column("last_modified")
    private long lastModified;
    
    @Column("received_at")
    private LocalDateTime receivedAt;
    
    @Column("username")
    private String username;

    public ReactiveFileData(String originalFilePath, String content, long fileSize, long lastModified, String username) {
        this.originalFilePath = originalFilePath;
        this.content = content;
        this.fileSize = fileSize;
        this.lastModified = lastModified;
        this.username = username;
        this.receivedAt = LocalDateTime.now();
    }
}
