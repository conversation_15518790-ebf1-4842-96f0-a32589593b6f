package com.folder.sync.server.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.folder.sync.server.domain.model.FileProcessingResult;
import com.folder.sync.server.service.FileDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Component
public class FileDataWebSocketHandler extends TextWebSocketHandler {

    private final FileDataService fileDataService;
    private final ObjectMapper objectMapper;
    private final ExecutorService executorService;

    @Autowired
    public FileDataWebSocketHandler(FileDataService fileDataService, ObjectMapper objectMapper) {
        this.fileDataService = fileDataService;
        this.objectMapper = objectMapper;
        this.executorService = Executors.newFixedThreadPool(5);
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        log.info("✅WebSocket connection established: " + session.getId());
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {
        executorService.submit(() -> {
            try {
                // Parse incoming message
                FileProcessingResult result = objectMapper.readValue(message.getPayload(), FileProcessingResult.class);
                
                if (result.isSuccess()) {
                    // Process file data
                    fileDataService.processFileData(
                        result.getData().getFilePath(),
                        result.getData().getContent(),
                        result.getData().getFileSize(),
                        result.getData().getLastModified()
                    );
                } else {
                    log.error("❌ Error processing file: " + result.getMessage());
                }
            } catch (Exception e) {
                log.error("❌ Error handling WebSocket message: " + e.getMessage());
            }
        });
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.error("WebSocket connection closed: " + session.getId());
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket transport error: " + exception.getMessage());
    }
}
