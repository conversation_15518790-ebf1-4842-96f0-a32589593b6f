package com.folder.sync.server.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.folder.sync.server.domain.model.FileProcessingResult;
import com.folder.sync.server.service.ReactiveFileDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Component
public class ReactiveFileDataWebSocketHandler extends TextWebSocketHandler {

    private final ReactiveFileDataService fileDataService;
    private final ObjectMapper objectMapper;

    @Autowired
    public ReactiveFileDataWebSocketHandler(ReactiveFileDataService fileDataService, ObjectMapper objectMapper) {
        this.fileDataService = fileDataService;
        this.objectMapper = objectMapper;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        log.info("✅ Reactive WebSocket connection established: " + session.getId());
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {
        // Handle message reactively
        Mono.fromCallable(() -> {
            try {
                // Parse incoming message
                return objectMapper.readValue(message.getPayload(), FileProcessingResult.class);
            } catch (Exception e) {
                throw new RuntimeException("Failed to parse message", e);
            }
        })
        .flatMap(result -> {
            if (result.isSuccess()) {
                // Process file data reactively
                return fileDataService.processFileData(
                    result.getData().getFilePath(),
                    result.getData().getContent(),
                    result.getData().getFileSize(),
                    result.getData().getLastModified(),
                    "unknown" // Default username for this handler
                );
            } else {
                log.error("❌ Error processing file: " + result.getMessage());
                return Mono.empty();
            }
        })
        .doOnSuccess(fileData -> {
            if (fileData != null) {
                log.info("✅ Successfully processed file: {}", fileData.getOriginalFilePath());
            }
        })
        .doOnError(error -> log.error("❌ Error handling reactive WebSocket message: {}", error.getMessage(), error))
        .subscribeOn(Schedulers.boundedElastic())
        .subscribe();
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("❌ Reactive WebSocket transport error for session {}: {}", session.getId(), exception.getMessage(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        log.info("🔌 Reactive WebSocket connection closed: {} with status: {}", session.getId(), closeStatus);
    }
}
