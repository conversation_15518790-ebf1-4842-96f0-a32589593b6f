package com.folder.sync.server.websocket;

import java.security.Principal;

public class User<PERSON>rincipal implements Principal {
    
    private final String username;
    
    public UserPrincipal(String username) {
        this.username = username;
    }
    
    @Override
    public String getName() {
        return username;
    }
    
    public String getUsername() {
        return username;
    }
    
    @Override
    public String toString() {
        return "UserPrincipal{username='" + username + "'}";
    }
}
