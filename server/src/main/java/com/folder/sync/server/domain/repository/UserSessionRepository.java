package com.folder.sync.server.domain.repository;

import com.folder.sync.server.domain.model.UserSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserSessionRepository extends JpaRepository<UserSession, Long> {
    
    Optional<UserSession> findBySessionId(String sessionId);
    
    List<UserSession> findByUsernameAndIsActive(String username, Boolean isActive);
    
    List<UserSession> findByIsActive(Boolean isActive);
    
    @Query("SELECT us FROM UserSession us WHERE us.username = :username AND us.isActive = true")
    List<UserSession> findActiveSessionsByUsername(@Param("username") String username);
    
    @Query("SELECT COUNT(us) FROM UserSession us WHERE us.isActive = true")
    long countActiveSessions();
    
    void deleteBySessionId(String sessionId);
}
