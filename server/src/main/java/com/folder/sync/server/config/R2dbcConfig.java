package com.folder.sync.server.config;

import io.r2dbc.spi.ConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.r2dbc.config.AbstractR2dbcConfiguration;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.connection.init.ConnectionFactoryInitializer;
import org.springframework.r2dbc.connection.init.ResourceDatabasePopulator;
import org.springframework.r2dbc.connection.R2dbcTransactionManager;
import org.springframework.transaction.ReactiveTransactionManager;

@Configuration
@org.springframework.context.annotation.Profile("reactive")
@EnableR2dbcRepositories(
    basePackages = "com.folder.sync.server.domain.repository",
    includeFilters = @org.springframework.context.annotation.ComponentScan.Filter(
        pattern = ".*Reactive.*Repository"
    )
)
public class R2dbcConfig extends AbstractR2dbcConfiguration {

    @Value("${spring.r2dbc.url}")
    private String r2dbcUrl;

    @Value("${spring.r2dbc.username}")
    private String username;

    @Value("${spring.r2dbc.password}")
    private String password;

    @Override
    @Bean
    public ConnectionFactory connectionFactory() {
        return io.r2dbc.spi.ConnectionFactories.get(
            io.r2dbc.spi.ConnectionFactoryOptions.builder()
                .option(io.r2dbc.spi.ConnectionFactoryOptions.DRIVER, "h2")
                .option(io.r2dbc.spi.ConnectionFactoryOptions.PROTOCOL, "mem")
                .option(io.r2dbc.spi.ConnectionFactoryOptions.DATABASE, "folder_sync")
                .option(io.r2dbc.spi.ConnectionFactoryOptions.USER, username)
                .option(io.r2dbc.spi.ConnectionFactoryOptions.PASSWORD, password)
                .build()
        );
    }

    @Bean
    public ConnectionFactoryInitializer initializer(ConnectionFactory connectionFactory) {
        ConnectionFactoryInitializer initializer = new ConnectionFactoryInitializer();
        initializer.setConnectionFactory(connectionFactory);
        
        ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
        populator.addScript(new ClassPathResource("schema.sql"));
        initializer.setDatabasePopulator(populator);
        
        return initializer;
    }
}
