package com.folder.sync.server.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServerFileListMessage {
    /**
     * List of file names that the server already has
     */
    private List<String> fileNames;
    
    /**
     * Message type identifier
     */
    private String messageType = "FILE_LIST";
    
    /**
     * Optional message for additional information
     */
    private String message;
    
    public ServerFileListMessage(List<String> fileNames) {
        this.fileNames = fileNames;
        this.messageType = "FILE_LIST";
    }
    
    public ServerFileListMessage(List<String> fileNames, String message) {
        this.fileNames = fileNames;
        this.messageType = "FILE_LIST";
        this.message = message;
    }
}
