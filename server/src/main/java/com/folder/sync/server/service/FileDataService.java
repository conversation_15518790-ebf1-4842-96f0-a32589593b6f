package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.repository.FileDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
@Slf4j
public class FileDataService {

    private final FileDataRepository fileDataRepository;
    
    @Value("${file.output-dir}")
    private String outputDir;

    @Value("${file.max-file-size}")
    private long maxFileSize;

    public FileDataService(FileDataRepository fileDataRepository) {
        this.fileDataRepository = fileDataRepository;
    }

    /**
     * Processes file data reactively
     */
    public Mono<FileData> processFileData(String filePath, String content, long fileSize, long lastModified, String username) {
        return Mono.fromCallable(() -> {
            // Validate file size
            if (fileSize > maxFileSize) {
                throw new IllegalArgumentException("File size exceeds maximum allowed size");
            }
            return new FileData(filePath, content, fileSize, lastModified, username);
        })
        .flatMap(fileDataRepository::save)
        .flatMap(savedFileData -> 
            writeToUserFileReactive(username, filePath, content)
                .thenReturn(savedFileData)
        )
        .doOnSuccess(fileData -> log.info("Successfully processed file: {} for user: {}", filePath, username))
        .doOnError(error -> log.error("Error processing file: {} for user: {}: {}", filePath, username, error.getMessage()));
    }

    /**
     * Backward compatibility method
     */
    public Mono<ReactiveFileData> processFileData(String filePath, String content, long fileSize, long lastModified) {
        return processFileData(filePath, content, fileSize, lastModified, "unknown");
    }

    /**
     * Creates a user-specific directory reactively
     */
    public Mono<Void> createUserDirectory(String username) {
        return Mono.fromRunnable(() -> {
            try {
                Path userDirPath = getUserDirectoryPath(username);
                Files.createDirectories(userDirPath);
                log.info("Created/verified user directory for user: {} at path: {}", username, userDirPath);
            } catch (IOException e) {
                throw new RuntimeException("Failed to create user directory", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .then();
    }

    /**
     * Gets the path to a user's directory
     */
    private Path getUserDirectoryPath(String username) {
        return Paths.get(outputDir, username);
    }

    /**
     * Writes file to user-specific directory reactively
     */
    private Mono<Void> writeToUserFileReactive(String username, String originalFilePath, String content) {
        return Mono.fromRunnable(() -> {
            try {
                writeToUserFile(username, originalFilePath, content);
            } catch (IOException e) {
                throw new RuntimeException("Failed to write file to disk", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .then();
    }

    /**
     * Synchronous file writing (called from reactive wrapper)
     */
    private void writeToUserFile(String username, String originalFilePath, String content) throws IOException {
        // Create user directory if it doesn't exist
        Path userDirPath = getUserDirectoryPath(username);
        Files.createDirectories(userDirPath);

        // Generate unique filename based on original path and timestamp
        String fileName = UUID.randomUUID() + "_" +
                          originalFilePath.replaceAll("\\\\", "_").replaceAll("/", "_") +
                          "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".txt";

        Path outputPath = userDirPath.resolve(fileName);

        // Write content to file
        try (BufferedWriter writer = Files.newBufferedWriter(outputPath)) {
            writer.write(content);
        }

        log.debug("Wrote file {} for user {} to path: {}", originalFilePath, username, outputPath);
    }

    /**
     * Gets the list of all file names that the server has received for a specific user
     */
    public Flux<String> getExistingFileNamesForUser(String username) {
        return fileDataRepository.findDistinctOriginalFilePathsByUsername(username);
    }

    /**
     * Gets the list of all file names that the server has received (all users)
     */
    public Flux<String> getExistingFileNames() {
        return fileDataRepository.findAll()
                .map(ReactiveFileData::getOriginalFilePath)
                .distinct();
    }

    /**
     * Gets the count of files the server has for a specific user
     */
    public Mono<Long> getFileCountForUser(String username) {
        return fileDataRepository.countByUsername(username);
    }

    /**
     * Gets the count of files the server has (all users)
     */
    public Mono<Long> getFileCount() {
        return fileDataRepository.count();
    }

    /**
     * Gets recent files for a specific user
     */
    public Flux<ReactiveFileData> getRecentFilesForUser(String username) {
        return fileDataRepository.findByUsernameOrderByReceivedAtDesc(username);
    }
}
