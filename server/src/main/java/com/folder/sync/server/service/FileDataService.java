package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.repository.FileDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FileDataService {

    private final FileDataRepository fileDataRepository;
    
    @Value("${file.output-dir}")
    private String outputDir;

    @Value("${file.max-file-size}")
    private long maxFileSize;

    public FileDataService(FileDataRepository fileDataRepository) {
        this.fileDataRepository = fileDataRepository;
    }

    @Transactional
    public void processFileData(String filePath, String content, long fileSize, long lastModified, String username) {
        // Validate file size
        if (fileSize > maxFileSize) {
            throw new IllegalArgumentException("File size exceeds maximum allowed size");
        }

        // Save to database
        FileData fileData = new FileData();
        fileData.setOriginalFilePath(filePath);
        fileData.setContent(content);
        fileData.setFileSize(fileSize);
        fileData.setLastModified(lastModified);
        fileData.setUsername(username);
        fileDataRepository.save(fileData);

        // Write to file in user-specific directory
        try {
            writeToUserFile(username, filePath, content);
        } catch (IOException e) {
            throw new RuntimeException("Failed to write file to disk", e);
        }
    }

    @Transactional
    public void processFileData(String filePath, String content, long fileSize, long lastModified) {
        // Backward compatibility - use "unknown" as default username
        processFileData(filePath, content, fileSize, lastModified, "unknown");
    }

    /**
     * Creates a user-specific directory if it doesn't exist
     */
    public void createUserDirectory(String username) throws IOException {
        Path userDirPath = getUserDirectoryPath(username);
        Files.createDirectories(userDirPath);
        log.info("Created/verified user directory for user: {} at path: {}", username, userDirPath);
    }

    /**
     * Gets the path to a user's directory
     */
    private Path getUserDirectoryPath(String username) {
        return Paths.get(outputDir, username);
    }

    /**
     * Writes file to user-specific directory
     */
    private void writeToUserFile(String username, String originalFilePath, String content) throws IOException {
        // Create user directory if it doesn't exist
        Path userDirPath = getUserDirectoryPath(username);
        Files.createDirectories(userDirPath);

        // Generate unique filename based on original path and timestamp
        String fileName = UUID.randomUUID() + "_" +
                          originalFilePath.replaceAll("\\\\", "_").replaceAll("/", "_") +
                          "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".txt";

        Path outputPath = userDirPath.resolve(fileName);

        // Write content to file
        try (BufferedWriter writer = Files.newBufferedWriter(outputPath)) {
            writer.write(content);
        }

        log.debug("Wrote file {} for user {} to path: {}", originalFilePath, username, outputPath);
    }

    private void writeToFile(String originalFilePath, String content) throws IOException {
        // Backward compatibility - write to root directory
        writeToUserFile("unknown", originalFilePath, content);
    }

    /**
     * Gets the list of all file names that the server has received for a specific user
     *
     * @param username The username to get files for
     * @return List of original file paths (file names) that the user has uploaded
     */
    public List<String> getExistingFileNamesForUser(String username) {
        return fileDataRepository.findDistinctOriginalFilePathsByUsername(username);
    }

    /**
     * Gets the list of all file names that the server has received (all users)
     *
     * @return List of original file paths (file names) that the server has
     */
    public List<String> getExistingFileNames() {
        return fileDataRepository.findAll()
                .stream()
                .map(FileData::getOriginalFilePath)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * Gets the count of files the server has for a specific user
     *
     * @param username The username to count files for
     * @return Total number of files for the user
     */
    public long getFileCountForUser(String username) {
        return fileDataRepository.countByUsername(username);
    }

    /**
     * Gets the count of files the server has (all users)
     *
     * @return Total number of unique files
     */
    public long getFileCount() {
        return fileDataRepository.count();
    }

    /**
     * Gets recent files for a specific user
     *
     * @param username The username to get files for
     * @return List of recent files for the user
     */
    public List<FileData> getRecentFilesForUser(String username) {
        return fileDataRepository.findByUsernameOrderByReceivedAtDesc(username);
    }
}
