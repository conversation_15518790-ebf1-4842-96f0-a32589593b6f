package com.folder.sync.server.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@org.springframework.context.annotation.Profile("traditional")
@EnableJpaRepositories(
    basePackages = "com.folder.sync.server.domain.repository",
    excludeFilters = @org.springframework.context.annotation.ComponentScan.Filter(
        pattern = ".*Reactive.*Repository"
    )
)
public class JpaConfig {
}
