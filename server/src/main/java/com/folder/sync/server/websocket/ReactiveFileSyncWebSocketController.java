package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.FileEvent;
import com.folder.sync.server.domain.model.FileProcessingResult;
import com.folder.sync.server.domain.model.ServerFileListMessage;
import com.folder.sync.server.service.ReactiveFileDataService;
import com.folder.sync.server.service.ReactiveUserSessionService;
import com.folder.sync.server.service.ReactiveSessionMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;

@Controller
public class ReactiveFileSyncWebSocketController {

    private static final Logger log = LoggerFactory.getLogger(ReactiveFileSyncWebSocketController.class);

    private final ReactiveFileDataService fileDataService;
    private final ReactiveUserSessionService userSessionService;
    private final ReactiveSessionMonitorService sessionMonitorService;
    private final SimpMessagingTemplate messagingTemplate;

    @Autowired
    public ReactiveFileSyncWebSocketController(ReactiveFileDataService fileDataService,
                                             ReactiveUserSessionService userSessionService,
                                             ReactiveSessionMonitorService sessionMonitorService,
                                             SimpMessagingTemplate messagingTemplate) {
        this.fileDataService = fileDataService;
        this.userSessionService = userSessionService;
        this.sessionMonitorService = sessionMonitorService;
        this.messagingTemplate = messagingTemplate;
    }

    @MessageMapping("/file-sync")
    public void handleFileSync(@Payload FileProcessingResult result, SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        
        // Get username reactively
        userSessionService.getUsernameBySessionId(sessionId)
            .switchIfEmpty(Mono.just("unknown"))
            .flatMap(username -> {
                if (result.isSuccess() && result.getData() != null) {
                    return processFileEvent(result.getData(), username, sessionId)
                        .doOnSuccess(v -> log.info("Successfully processed file: {} from user: {}", 
                            result.getData().getFilePath(), username))
                        .doOnError(error -> log.error("Error processing file sync message from user {}: {}", 
                            username, error.getMessage(), error));
                } else {
                    log.error("❌ Error processing file: {}", result.getMessage());
                    return Mono.empty();
                }
            })
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }

    private Mono<Void> processFileEvent(FileEvent fileEvent, String username, String sessionId) {
        return fileDataService.processFileData(
                fileEvent.getFilePath(),
                fileEvent.getContent(),
                fileEvent.getFileSize(),
                fileEvent.getLastModified(),
                username
            )
            .flatMap(savedFile -> 
                sessionMonitorService.broadcastSessionEvent(
                    "FILE_UPLOADED",
                    sessionId,
                    username,
                    "File: " + fileEvent.getFilePath()
                )
            );
    }

    @MessageMapping("/connect")
    public void handleConnect(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        String username = headerAccessor.getUser() != null ? headerAccessor.getUser().getName() : "unknown";

        log.info("🔗 User {} connected with session ID: {}", username, sessionId);

        // Handle connection reactively
        userSessionService.createSession(sessionId, username)
            .flatMap(session -> fileDataService.createUserDirectory(username).thenReturn(session))
            .flatMap(session -> 
                sessionMonitorService.broadcastSessionEvent(
                    "CONNECTED",
                    sessionId,
                    username,
                    "User connected and directory created"
                )
                .then(sessionMonitorService.broadcastSessionStats())
                .thenReturn(session)
            )
            .flatMap(session -> sendFileListToUser(username, sessionId))
            .doOnSuccess(v -> log.info("✅ Connection setup completed for user: {}", username))
            .doOnError(error -> log.error("❌ Error during connection setup for user {}: {}", username, error.getMessage(), error))
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }

    @MessageMapping("/disconnect")
    public void handleDisconnect(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        
        userSessionService.getUsernameBySessionId(sessionId)
            .flatMap(username -> {
                log.info("🔌 User {} disconnected with session ID: {}", username, sessionId);
                
                return userSessionService.disconnectSession(sessionId)
                    .then(sessionMonitorService.broadcastSessionEvent(
                        "DISCONNECTED",
                        sessionId,
                        username,
                        "User disconnected"
                    ))
                    .then(sessionMonitorService.broadcastSessionStats());
            })
            .doOnError(error -> log.error("❌ Error during disconnect for session {}: {}", sessionId, error.getMessage(), error))
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }

    @MessageMapping("/request-file-list")
    public void handleFileListRequest(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        
        userSessionService.getUsernameBySessionId(sessionId)
            .flatMap(username -> sendFileListToUser(username, sessionId))
            .doOnError(error -> log.error("❌ Error sending file list for session {}: {}", sessionId, error.getMessage(), error))
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }

    private Mono<Void> sendFileListToUser(String username, String sessionId) {
        return fileDataService.getExistingFileNamesForUser(username)
            .collectList()
            .map(fileNames -> {
                ServerFileListMessage fileListMessage = new ServerFileListMessage();
                fileListMessage.setFileNames(fileNames);
                fileListMessage.setMessage(String.format("You have %d files on the server", fileNames.size()));
                return fileListMessage;
            })
            .doOnNext(message -> {
                messagingTemplate.convertAndSendToUser(
                    sessionId,
                    "/queue/file-list",
                    message
                );
                log.info("📋 Sent file list with {} files to user: {} (session: {})", 
                    message.getFileNames().size(), username, sessionId);
            })
            .then();
    }
}
