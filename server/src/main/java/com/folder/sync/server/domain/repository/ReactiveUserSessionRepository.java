package com.folder.sync.server.domain.repository;

import com.folder.sync.server.domain.model.ReactiveUserSession;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface ReactiveUserSessionRepository extends R2dbcRepository<ReactiveUserSession, Long> {

    Mono<ReactiveUserSession> findBySessionId(String sessionId);

    Flux<ReactiveUserSession> findByUsernameAndIsActive(String username, Boolean isActive);

    Flux<ReactiveUserSession> findByIsActive(Boolean isActive);

    @Query("SELECT * FROM user_sessions WHERE username = :username AND is_active = true")
    Flux<ReactiveUserSession> findActiveSessionsByUsername(@Param("username") String username);
    
    @Query("SELECT COUNT(*) FROM user_sessions WHERE is_active = true")
    Mono<Long> countActiveSessions();
    
    Mono<Void> deleteBySessionId(String sessionId);
}
