package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.service.ReactiveSessionMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import reactor.core.scheduler.Schedulers;

@Controller
@Slf4j
public class ReactiveSessionMonitorController {
    
    private final ReactiveSessionMonitorService sessionMonitorService;
    private final SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    public ReactiveSessionMonitorController(ReactiveSessionMonitorService sessionMonitorService,
                                          SimpMessagingTemplate messagingTemplate) {
        this.sessionMonitorService = sessionMonitorService;
        this.messagingTemplate = messagingTemplate;
    }
    
    /**
     * Handles requests for current session statistics
     */
    @MessageMapping("/monitor/stats")
    public void handleStatsRequest(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        log.info("Received session stats request from session: {}", sessionId);
        
        sessionMonitorService.getCurrentStats()
            .map(SessionMonitorMessage::createStatsMessage)
            .doOnNext(message -> {
                messagingTemplate.convertAndSendToUser(
                    sessionId,
                    "/queue/session-monitor",
                    message
                );
                log.debug("Sent session stats to session: {}", sessionId);
            })
            .doOnError(error -> log.error("Error handling stats request from session {}: {}", sessionId, error.getMessage(), error))
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }
    
    /**
     * Handles requests for current active sessions list
     */
    @MessageMapping("/monitor/sessions")
    public void handleSessionListRequest(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        log.info("Received session list request from session: {}", sessionId);
        
        sessionMonitorService.getCurrentSessions()
            .collectList()
            .zipWith(sessionMonitorService.getCurrentStats())
            .map(tuple -> SessionMonitorMessage.createSessionListMessage(tuple.getT1(), tuple.getT2()))
            .doOnNext(message -> {
                messagingTemplate.convertAndSendToUser(
                    sessionId,
                    "/queue/session-monitor",
                    message
                );
                log.debug("Sent session list with {} sessions to session: {}",
                    message.getActiveSessions().size(), sessionId);
            })
            .doOnError(error -> log.error("Error handling session list request from session {}: {}", sessionId, error.getMessage(), error))
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }
    
    /**
     * Handles subscription requests for real-time monitoring
     */
    @MessageMapping("/monitor/subscribe")
    public void handleSubscriptionRequest(SessionMonitorMessage.SubscriptionRequest request, 
                                        SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        log.info("Received subscription request from session: {} with options: {}", sessionId, request);
        
        // Send initial data based on subscription type
        if (request.isIncludeStats()) {
            sessionMonitorService.getCurrentStats()
                .map(SessionMonitorMessage::createStatsMessage)
                .doOnNext(statsMessage -> {
                    messagingTemplate.convertAndSendToUser(
                        sessionId,
                        "/queue/session-monitor",
                        statsMessage
                    );
                })
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
        }
        
        if (request.isIncludeSessions()) {
            sessionMonitorService.getCurrentSessions()
                .collectList()
                .zipWith(sessionMonitorService.getCurrentStats())
                .map(tuple -> SessionMonitorMessage.createSessionListMessage(tuple.getT1(), tuple.getT2()))
                .doOnNext(listMessage -> {
                    messagingTemplate.convertAndSendToUser(
                        sessionId,
                        "/queue/session-monitor",
                        listMessage
                    );
                })
                .doOnSuccess(v -> log.debug("Sent initial subscription data to session: {}", sessionId))
                .doOnError(error -> log.error("Error handling subscription request from session {}: {}", sessionId, error.getMessage(), error))
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
        }
    }
}
