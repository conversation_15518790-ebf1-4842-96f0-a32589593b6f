package com.folder.sync.server;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;

@SpringBootApplication
@EnableR2dbcRepositories
@Slf4j
public class FolderSyncServerApplication {
    public static void main(String[] args) {
        log.info("🚀 Starting Reactive Folder Sync Server...");
        SpringApplication.run(FolderSyncServerApplication.class, args);
        log.info("✅ Reactive Folder Sync Server started successfully!");
    }
}
