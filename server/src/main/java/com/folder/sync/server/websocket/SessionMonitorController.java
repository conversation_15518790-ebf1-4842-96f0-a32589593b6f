package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.service.SessionMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.util.List;

@Controller
@Slf4j
public class SessionMonitorController {
    
    private final SessionMonitorService sessionMonitorService;
    private final SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    public SessionMonitorController(SessionMonitorService sessionMonitorService,
                                  SimpMessagingTemplate messagingTemplate) {
        this.sessionMonitorService = sessionMonitorService;
        this.messagingTemplate = messagingTemplate;
    }
    
    /**
     * Handles requests for current session statistics
     */
    @MessageMapping("/monitor/stats")
    public void handleStatsRequest(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        log.info("Received session stats request from session: {}", sessionId);
        
        try {
            // Send current stats to the requesting client
            SessionMonitorMessage.SessionStats stats = sessionMonitorService.getCurrentStats();
            SessionMonitorMessage message = SessionMonitorMessage.createStatsMessage(stats);
            
            messagingTemplate.convertAndSendToUser(
                sessionId,
                "/queue/session-monitor",
                message
            );
            
            log.debug("Sent session stats to session: {}", sessionId);
        } catch (Exception e) {
            log.error("Error handling stats request from session {}: {}", sessionId, e.getMessage(), e);
        }
    }
    
    /**
     * Handles requests for current active sessions list
     */
    @MessageMapping("/monitor/sessions")
    public void handleSessionListRequest(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        log.info("Received session list request from session: {}", sessionId);
        
        try {
            // Send current session list to the requesting client
            List<SessionMonitorMessage.SessionInfo> sessions = sessionMonitorService.getCurrentSessions();
            SessionMonitorMessage.SessionStats stats = sessionMonitorService.getCurrentStats();
            SessionMonitorMessage message = SessionMonitorMessage.createSessionListMessage(sessions, stats);
            
            messagingTemplate.convertAndSendToUser(
                sessionId,
                "/queue/session-monitor",
                message
            );
            
            log.debug("Sent session list with {} sessions to session: {}", sessions.size(), sessionId);
        } catch (Exception e) {
            log.error("Error handling session list request from session {}: {}", sessionId, e.getMessage(), e);
        }
    }
    
    /**
     * Handles subscription requests for real-time monitoring
     */
    @MessageMapping("/monitor/subscribe")
    public void handleMonitorSubscription(@Payload MonitorSubscriptionRequest request, 
                                        SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        log.info("Received monitor subscription request from session: {} with options: {}", sessionId, request);
        
        try {
            // Send initial data based on subscription type
            if (request.isIncludeStats()) {
                SessionMonitorMessage.SessionStats stats = sessionMonitorService.getCurrentStats();
                SessionMonitorMessage statsMessage = SessionMonitorMessage.createStatsMessage(stats);
                
                messagingTemplate.convertAndSendToUser(
                    sessionId,
                    "/queue/session-monitor",
                    statsMessage
                );
            }
            
            if (request.isIncludeSessions()) {
                List<SessionMonitorMessage.SessionInfo> sessions = sessionMonitorService.getCurrentSessions();
                SessionMonitorMessage.SessionStats stats = sessionMonitorService.getCurrentStats();
                SessionMonitorMessage listMessage = SessionMonitorMessage.createSessionListMessage(sessions, stats);
                
                messagingTemplate.convertAndSendToUser(
                    sessionId,
                    "/queue/session-monitor",
                    listMessage
                );
            }
            
            log.info("Successfully set up monitoring subscription for session: {}", sessionId);
        } catch (Exception e) {
            log.error("Error handling monitor subscription from session {}: {}", sessionId, e.getMessage(), e);
        }
    }
    
    /**
     * Request model for monitor subscription
     */
    public static class MonitorSubscriptionRequest {
        private boolean includeStats = true;
        private boolean includeSessions = true;
        private boolean includeEvents = true;
        private int updateIntervalSeconds = 5;
        
        // Getters and setters
        public boolean isIncludeStats() { return includeStats; }
        public void setIncludeStats(boolean includeStats) { this.includeStats = includeStats; }
        
        public boolean isIncludeSessions() { return includeSessions; }
        public void setIncludeSessions(boolean includeSessions) { this.includeSessions = includeSessions; }
        
        public boolean isIncludeEvents() { return includeEvents; }
        public void setIncludeEvents(boolean includeEvents) { this.includeEvents = includeEvents; }
        
        public int getUpdateIntervalSeconds() { return updateIntervalSeconds; }
        public void setUpdateIntervalSeconds(int updateIntervalSeconds) { this.updateIntervalSeconds = updateIntervalSeconds; }
        
        @Override
        public String toString() {
            return String.format("MonitorSubscriptionRequest{stats=%s, sessions=%s, events=%s, interval=%ds}", 
                includeStats, includeSessions, includeEvents, updateIntervalSeconds);
        }
    }
}
