package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.service.SessionMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

@Controller
@Slf4j
public class SessionMonitorController {

    private final SessionMonitorService sessionMonitorService;
    private final SimpMessagingTemplate messagingTemplate;

    @Autowired
    public SessionMonitorController(SessionMonitorService sessionMonitorService,
                                  SimpMessagingTemplate messagingTemplate) {
        this.sessionMonitorService = sessionMonitorService;
        this.messagingTemplate = messagingTemplate;
    }

    @MessageMapping("/monitor/stats")
    @SendToUser("/queue/monitor-stats")
    public Mono<SessionMonitorMessage.SessionStats> getSessionStats() {
        log.debug("Received request for session statistics");
        return sessionMonitorService.getCurrentStats()
            .doOnSuccess(stats -> log.debug("Sent session stats: {} active sessions", stats.getTotalActiveSessions()))
            .doOnError(error -> log.error("Error getting session stats: {}", error.getMessage()));
    }

    @MessageMapping("/monitor/sessions")
    @SendToUser("/queue/monitor-sessions")
    public Mono<SessionMonitorMessage> getActiveSessions() {
        log.debug("Received request for active sessions list");
        return sessionMonitorService.getCurrentSessions()
            .collectList()
            .map(sessions -> {
                SessionMonitorMessage message = new SessionMonitorMessage();
                message.setType("SESSION_LIST");
                message.setSessions(sessions);
                return message;
            })
            .doOnSuccess(message -> log.debug("Sent {} active sessions", message.getSessions().size()))
            .doOnError(error -> log.error("Error getting active sessions: {}", error.getMessage()));
    }
}
