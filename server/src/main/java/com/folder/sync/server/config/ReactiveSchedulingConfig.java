package com.folder.sync.server.config;

import com.folder.sync.server.service.ReactiveSessionMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import reactor.core.Disposable;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Configuration
@org.springframework.context.annotation.Profile("reactive")
@Slf4j
public class ReactiveSchedulingConfig {
    
    private final ReactiveSessionMonitorService sessionMonitorService;
    private final List<Disposable> scheduledStreams = new ArrayList<>();
    
    @Autowired
    public ReactiveSchedulingConfig(ReactiveSessionMonitorService sessionMonitorService) {
        this.sessionMonitorService = sessionMonitorService;
    }
    
    /**
     * Start reactive streams when the application is ready
     */
    @EventListener(ApplicationReadyEvent.class)
    public void startReactiveScheduling() {
        log.info("Starting reactive scheduling streams...");
        
        // Start periodic session stats broadcasting every 30 seconds
        Disposable statsStream = sessionMonitorService
            .createPeriodicStatsStream(Duration.ofSeconds(30))
            .subscribe(
                null,
                error -> log.error("Error in reactive stats stream: {}", error.getMessage(), error),
                () -> log.info("Reactive stats stream completed")
            );
        scheduledStreams.add(statsStream);
        
        // Start periodic session list broadcasting every 2 minutes
        Disposable sessionListStream = sessionMonitorService
            .createPeriodicSessionListStream(Duration.ofMinutes(2))
            .subscribe(
                null,
                error -> log.error("Error in reactive session list stream: {}", error.getMessage(), error),
                () -> log.info("Reactive session list stream completed")
            );
        scheduledStreams.add(sessionListStream);
        
        log.info("Reactive scheduling streams started successfully");
    }
    
    /**
     * Clean up streams on shutdown
     */
    public void stopReactiveScheduling() {
        log.info("Stopping reactive scheduling streams...");
        scheduledStreams.forEach(disposable -> {
            if (!disposable.isDisposed()) {
                disposable.dispose();
            }
        });
        scheduledStreams.clear();
        log.info("Reactive scheduling streams stopped");
    }
}
