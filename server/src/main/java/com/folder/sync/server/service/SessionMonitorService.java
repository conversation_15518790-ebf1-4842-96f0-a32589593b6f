package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.domain.model.UserSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SessionMonitorService {
    
    private final UserSessionService userSessionService;
    private final FileDataService fileDataService;
    private final SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    public SessionMonitorService(UserSessionService userSessionService, 
                               FileDataService fileDataService,
                               SimpMessagingTemplate messagingTemplate) {
        this.userSessionService = userSessionService;
        this.fileDataService = fileDataService;
        this.messagingTemplate = messagingTemplate;
    }
    
    /**
     * Broadcasts session statistics to all monitoring clients
     */
    public void broadcastSessionStats() {
        try {
            SessionMonitorMessage.SessionStats stats = generateSessionStats();
            SessionMonitorMessage message = SessionMonitorMessage.createStatsMessage(stats);
            
            messagingTemplate.convertAndSend("/topic/session-monitor", message);
            log.debug("Broadcasted session stats: {} active sessions", stats.getTotalActiveSessions());
        } catch (Exception e) {
            log.error("Error broadcasting session stats: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Broadcasts complete session list with stats to monitoring clients
     */
    public void broadcastSessionList() {
        try {
            List<UserSession> activeSessions = userSessionService.getAllActiveSessions();
            List<SessionMonitorMessage.SessionInfo> sessionInfos = activeSessions.stream()
                    .map(this::convertToSessionInfo)
                    .collect(Collectors.toList());
            
            SessionMonitorMessage.SessionStats stats = generateSessionStats();
            SessionMonitorMessage message = SessionMonitorMessage.createSessionListMessage(sessionInfos, stats);
            
            messagingTemplate.convertAndSend("/topic/session-monitor", message);
            log.debug("Broadcasted session list with {} active sessions", sessionInfos.size());
        } catch (Exception e) {
            log.error("Error broadcasting session list: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Broadcasts a session event to monitoring clients
     */
    public void broadcastSessionEvent(String eventType, String sessionId, String username, String details) {
        try {
            SessionMonitorMessage.SessionEvent event = new SessionMonitorMessage.SessionEvent(
                eventType, sessionId, username, LocalDateTime.now(), details
            );
            
            SessionMonitorMessage message = SessionMonitorMessage.createEventMessage(event);
            messagingTemplate.convertAndSend("/topic/session-monitor", message);
            
            log.debug("Broadcasted session event: {} for user {} (session: {})", eventType, username, sessionId);
        } catch (Exception e) {
            log.error("Error broadcasting session event: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Generates current session statistics
     */
    private SessionMonitorMessage.SessionStats generateSessionStats() {
        List<UserSession> activeSessions = userSessionService.getAllActiveSessions();
        long totalActiveSessions = activeSessions.size();
        long totalUsers = activeSessions.stream()
                .map(UserSession::getUsername)
                .distinct()
                .count();
        
        long totalFiles = fileDataService.getFileCount();
        
        double averageSessionDuration = activeSessions.stream()
                .mapToLong(session -> Duration.between(session.getConnectedAt(), LocalDateTime.now()).toMinutes())
                .average()
                .orElse(0.0);
        
        return new SessionMonitorMessage.SessionStats(
            totalActiveSessions,
            totalUsers,
            totalFiles,
            averageSessionDuration,
            LocalDateTime.now()
        );
    }
    
    /**
     * Converts UserSession to SessionInfo for monitoring
     */
    private SessionMonitorMessage.SessionInfo convertToSessionInfo(UserSession session) {
        long filesUploaded = fileDataService.getFileCountForUser(session.getUsername());
        long sessionDuration = Duration.between(session.getConnectedAt(), LocalDateTime.now()).toMinutes();
        
        return new SessionMonitorMessage.SessionInfo(
            session.getSessionId(),
            session.getUsername(),
            session.getConnectedAt(),
            session.getLastActivity(),
            session.getIsActive(),
            filesUploaded,
            "N/A", // IP address not tracked in current implementation
            sessionDuration
        );
    }
    
    /**
     * Gets current session statistics without broadcasting
     */
    public SessionMonitorMessage.SessionStats getCurrentStats() {
        return generateSessionStats();
    }
    
    /**
     * Gets current active sessions without broadcasting
     */
    public List<SessionMonitorMessage.SessionInfo> getCurrentSessions() {
        return userSessionService.getAllActiveSessions().stream()
                .map(this::convertToSessionInfo)
                .collect(Collectors.toList());
    }
}
