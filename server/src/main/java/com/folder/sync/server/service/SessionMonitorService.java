package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.UserSession;
import com.folder.sync.server.domain.model.SessionMonitorMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class SessionMonitorService {

    private final UserSessionService userSessionService;
    private final FileDataService fileDataService;
    private final SimpMessagingTemplate messagingTemplate;

    @Autowired
    public SessionMonitorService(UserSessionService userSessionService,
                                       FileDataService fileDataService,
                                       SimpMessagingTemplate messagingTemplate) {
        this.userSessionService = userSessionService;
        this.fileDataService = fileDataService;
        this.messagingTemplate = messagingTemplate;
    }
    
    /**
     * Broadcasts session statistics to all monitoring clients reactively
     */
    public Mono<Void> broadcastSessionStats() {
        return generateSessionStats()
            .map(SessionMonitorMessage::createStatsMessage)
            .doOnNext(message -> {
                messagingTemplate.convertAndSend("/topic/session-monitor", message);
                log.debug("Broadcasted session stats: {} active sessions",
                    message.getStats().getTotalActiveSessions());
            })
            .doOnError(error -> log.error("Error broadcasting session stats: {}", error.getMessage(), error))
            .then();
    }
    
    /**
     * Broadcasts complete session list with stats to monitoring clients reactively
     */
    public Mono<Void> broadcastSessionList() {
        return userSessionService.getAllActiveSessions()
            .flatMap(this::convertToSessionInfo)
            .collectList()
            .zipWith(generateSessionStats())
            .map(tuple -> SessionMonitorMessage.createSessionListMessage(tuple.getT1(), tuple.getT2()))
            .doOnNext(message -> {
                messagingTemplate.convertAndSend("/topic/session-monitor", message);
                log.debug("Broadcasted session list with {} active sessions",
                    message.getActiveSessions().size());
            })
            .doOnError(error -> log.error("Error broadcasting session list: {}", error.getMessage(), error))
            .then();
    }
    
    /**
     * Broadcasts a session event to monitoring clients reactively
     */
    public Mono<Void> broadcastSessionEvent(String eventType, String sessionId, String username, String details) {
        return Mono.fromRunnable(() -> {
            try {
                SessionMonitorMessage.SessionEvent event = new SessionMonitorMessage.SessionEvent(
                    eventType, sessionId, username, LocalDateTime.now(), details
                );
                
                SessionMonitorMessage message = SessionMonitorMessage.createEventMessage(event);
                messagingTemplate.convertAndSend("/topic/session-monitor", message);
                
                log.debug("Broadcasted session event: {} for user {} (session: {})", eventType, username, sessionId);
            } catch (Exception e) {
                log.error("Error broadcasting session event: {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .then();
    }
    
    /**
     * Generates current session statistics reactively
     */
    private Mono<SessionMonitorMessage.SessionStats> generateSessionStats() {
        return userSessionService.getAllActiveSessions()
            .collectList()
            .zipWith(fileDataService.getFileCount())
            .map(tuple -> {
                List<UserSession> activeSessions = tuple.getT1();
                long totalFiles = tuple.getT2();

                long totalActiveSessions = activeSessions.size();
                long totalUsers = activeSessions.stream()
                        .map(UserSession::getUsername)
                        .distinct()
                        .count();
                
                double averageSessionDuration = activeSessions.stream()
                        .mapToLong(session -> Duration.between(session.getConnectedAt(), LocalDateTime.now()).toMinutes())
                        .average()
                        .orElse(0.0);
                
                return new SessionMonitorMessage.SessionStats(
                    totalActiveSessions,
                    totalUsers,
                    totalFiles,
                    averageSessionDuration,
                    LocalDateTime.now()
                );
            });
    }
    
    /**
     * Converts UserSession to SessionInfo for monitoring reactively
     */
    private Mono<SessionMonitorMessage.SessionInfo> convertToSessionInfo(UserSession session) {
        return fileDataService.getFileCountForUser(session.getUsername())
            .map(filesUploaded -> {
                long sessionDuration = Duration.between(session.getConnectedAt(), LocalDateTime.now()).toMinutes();
                
                return new SessionMonitorMessage.SessionInfo(
                    session.getSessionId(),
                    session.getUsername(),
                    session.getConnectedAt(),
                    session.getLastActivity(),
                    session.getIsActive(),
                    filesUploaded,
                    "N/A", // IP address not tracked in current implementation
                    sessionDuration
                );
            });
    }
    
    /**
     * Gets current session statistics without broadcasting
     */
    public Mono<SessionMonitorMessage.SessionStats> getCurrentStats() {
        return generateSessionStats();
    }
    
    /**
     * Gets current sessions without broadcasting
     */
    public Flux<SessionMonitorMessage.SessionInfo> getCurrentSessions() {
        return userSessionService.getAllActiveSessions()
            .flatMap(this::convertToSessionInfo);
    }
    
    /**
     * Creates a reactive stream for periodic session stats broadcasting
     */
    public Flux<Void> createPeriodicStatsStream(Duration interval) {
        return Flux.interval(interval)
            .flatMap(tick -> broadcastSessionStats())
            .doOnError(error -> log.error("Error in periodic stats stream: {}", error.getMessage(), error))
            .onErrorContinue((error, item) -> log.warn("Continuing after error in stats stream: {}", error.getMessage()));
    }
    
    /**
     * Creates a reactive stream for periodic session list broadcasting
     */
    public Flux<Void> createPeriodicSessionListStream(Duration interval) {
        return Flux.interval(interval)
            .flatMap(tick -> broadcastSessionList())
            .doOnError(error -> log.error("Error in periodic session list stream: {}", error.getMessage(), error))
            .onErrorContinue((error, item) -> log.warn("Continuing after error in session list stream: {}", error.getMessage()));
    }
}
