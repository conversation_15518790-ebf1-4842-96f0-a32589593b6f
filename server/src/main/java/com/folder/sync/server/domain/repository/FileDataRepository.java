package com.folder.sync.server.domain.repository;

import com.folder.sync.server.domain.model.FileData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface FileDataRepository extends JpaRepository<FileData, Long> {
    @Query("SELECT f FROM FileData f ORDER BY f.receivedAt DESC")
    List<FileData> findTopByOrderByReceivedAtDesc(@Param("limit") int limit);

    List<FileData> findByUsername(String username);

    @Query("SELECT DISTINCT f.originalFilePath FROM FileData f WHERE f.username = :username")
    List<String> findDistinctOriginalFilePathsByUsername(@Param("username") String username);

    @Query("SELECT f FROM FileData f WHERE f.username = :username ORDER BY f.receivedAt DESC")
    List<FileData> findByUsernameOrderByReceivedAtDesc(@Param("username") String username);

    long countByUsername(String username);
}
