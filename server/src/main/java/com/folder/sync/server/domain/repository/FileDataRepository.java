package com.folder.sync.server.domain.repository;

import com.folder.sync.server.domain.model.FileData;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface FileDataRepository extends R2dbcRepository<FileData, Long> {
    
    @Query("SELECT * FROM file_data ORDER BY received_at DESC LIMIT :limit")
    Flux<FileData> findTopByOrderByReceivedAtDesc(@Param("limit") int limit);

    Flux<FileData> findByUsername(String username);

    @Query("SELECT DISTINCT original_file_path FROM file_data WHERE username = :username")
    Flux<String> findDistinctOriginalFilePathsByUsername(@Param("username") String username);

    @Query("SELECT * FROM file_data WHERE username = :username ORDER BY received_at DESC")
    Flux<FileData> findByUsernameOrderByReceivedAtDesc(@Param("username") String username);

    Mono<Long> countByUsername(String username);
}
