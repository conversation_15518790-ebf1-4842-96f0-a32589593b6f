package com.folder.sync.server.domain.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileSyncMessage {
    
    private String type;
    private String filePath;
    private String content;
    private long fileSize;
    private long lastModified;
    private String message;
    private List<String> fileList;
    private String username;
    private String sessionId;
    
    // Message types
    public static final String FILE_UPLOAD = "FILE_UPLOAD";
    public static final String FILE_UPLOAD_SUCCESS = "FILE_UPLOAD_SUCCESS";
    public static final String FILE_LIST_REQUEST = "FILE_LIST_REQUEST";
    public static final String FILE_LIST = "FILE_LIST";
    public static final String PING = "PING";
    public static final String PONG = "PONG";
    public static final String ERROR = "ERROR";
    public static final String CONNECT = "CONNECT";
    public static final String DISCONNECT = "DISCONNECT";
    
    // Convenience constructors
    public FileSyncMessage(String type, String message) {
        this.type = type;
        this.message = message;
    }
    
    public FileSyncMessage(String type, String filePath, String content, long fileSize, long lastModified) {
        this.type = type;
        this.filePath = filePath;
        this.content = content;
        this.fileSize = fileSize;
        this.lastModified = lastModified;
    }
    
    public FileSyncMessage(String type, List<String> fileList) {
        this.type = type;
        this.fileList = fileList;
    }
    
    // Helper methods
    public boolean isFileUpload() {
        return FILE_UPLOAD.equals(type);
    }
    
    public boolean isFileListRequest() {
        return FILE_LIST_REQUEST.equals(type);
    }
    
    public boolean isPing() {
        return PING.equals(type);
    }
    
    public boolean isConnect() {
        return CONNECT.equals(type);
    }
    
    public boolean isDisconnect() {
        return DISCONNECT.equals(type);
    }
    
    public static FileSyncMessage createFileUpload(String filePath, String content, long fileSize, long lastModified) {
        return new FileSyncMessage(FILE_UPLOAD, filePath, content, fileSize, lastModified);
    }
    
    public static FileSyncMessage createFileList(List<String> fileList) {
        return new FileSyncMessage(FILE_LIST, fileList);
    }
    
    public static FileSyncMessage createSuccess(String message) {
        return new FileSyncMessage(FILE_UPLOAD_SUCCESS, message);
    }
    
    public static FileSyncMessage createError(String message) {
        return new FileSyncMessage(ERROR, message);
    }
    
    public static FileSyncMessage createPing() {
        return new FileSyncMessage(PING, "ping");
    }
    
    public static FileSyncMessage createPong() {
        return new FileSyncMessage(PONG, "pong");
    }
}
