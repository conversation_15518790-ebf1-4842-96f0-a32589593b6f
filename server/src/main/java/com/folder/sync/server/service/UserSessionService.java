package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.UserSession;
import com.folder.sync.server.domain.repository.UserSessionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class UserSessionService {
    
    private final UserSessionRepository userSessionRepository;
    
    @Autowired
    public UserSessionService(UserSessionRepository userSessionRepository) {
        this.userSessionRepository = userSessionRepository;
    }
    
    /**
     * Creates a new user session when a client connects
     */
    @Transactional
    public UserSession createSession(String sessionId, String username) {
        log.info("Creating new session for user: {} with session ID: {}", username, sessionId);
        
        // Check if session already exists
        Optional<UserSession> existingSession = userSessionRepository.findBySessionId(sessionId);
        if (existingSession.isPresent()) {
            log.warn("Session {} already exists for user {}, updating it", sessionId, username);
            UserSession session = existingSession.get();
            session.setUsername(username);
            session.setIsActive(true);
            session.updateLastActivity();
            return userSessionRepository.save(session);
        }
        
        UserSession newSession = new UserSession(sessionId, username);
        UserSession savedSession = userSessionRepository.save(newSession);
        log.info("Created session {} for user {}", sessionId, username);
        return savedSession;
    }
    
    /**
     * Updates the last activity time for a session
     */
    @Transactional
    public void updateSessionActivity(String sessionId) {
        Optional<UserSession> session = userSessionRepository.findBySessionId(sessionId);
        if (session.isPresent()) {
            session.get().updateLastActivity();
            userSessionRepository.save(session.get());
        }
    }
    
    /**
     * Marks a session as disconnected when a client disconnects
     */
    @Transactional
    public void disconnectSession(String sessionId) {
        log.info("Disconnecting session: {}", sessionId);
        Optional<UserSession> session = userSessionRepository.findBySessionId(sessionId);
        if (session.isPresent()) {
            session.get().disconnect();
            userSessionRepository.save(session.get());
            log.info("Session {} marked as disconnected", sessionId);
        } else {
            log.warn("Attempted to disconnect non-existent session: {}", sessionId);
        }
    }
    
    /**
     * Gets the username associated with a session ID
     */
    public Optional<String> getUsernameBySessionId(String sessionId) {
        return userSessionRepository.findBySessionId(sessionId)
                .map(UserSession::getUsername);
    }
    
    /**
     * Gets all active sessions for a specific user
     */
    public List<UserSession> getActiveSessionsForUser(String username) {
        return userSessionRepository.findActiveSessionsByUsername(username);
    }
    
    /**
     * Gets all active sessions
     */
    public List<UserSession> getAllActiveSessions() {
        return userSessionRepository.findByIsActive(true);
    }
    
    /**
     * Gets the count of active sessions
     */
    public long getActiveSessionCount() {
        return userSessionRepository.countActiveSessions();
    }
    
    /**
     * Removes old inactive sessions (cleanup method)
     */
    @Transactional
    public void cleanupInactiveSessions() {
        List<UserSession> inactiveSessions = userSessionRepository.findByIsActive(false);
        log.info("Cleaning up {} inactive sessions", inactiveSessions.size());
        userSessionRepository.deleteAll(inactiveSessions);
    }
}
