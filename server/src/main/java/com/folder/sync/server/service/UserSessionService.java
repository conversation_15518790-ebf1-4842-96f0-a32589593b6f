package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.UserSession;
import com.folder.sync.server.domain.repository.UserSessionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class UserSessionService {

    private final UserSessionRepository userSessionRepository;

    @Autowired
    public UserSessionService(UserSessionRepository userSessionRepository) {
        this.userSessionRepository = userSessionRepository;
    }
    
    /**
     * Creates a new user session when a client connects
     */
    public Mono<UserSession> createSession(String sessionId, String username) {
        log.info("Creating new session for user: {} with session ID: {}", username, sessionId);

        return userSessionRepository.findBySessionId(sessionId)
            .flatMap(existingSession -> {
                log.warn("Session {} already exists for user {}, updating it", sessionId, username);
                existingSession.setUsername(username);
                existingSession.setIsActive(true);
                existingSession.updateLastActivity();
                return userSessionRepository.save(existingSession);
            })
            .switchIfEmpty(
                Mono.defer(() -> {
                    UserSession newSession = new UserSession(sessionId, username);
                    return userSessionRepository.save(newSession)
                        .doOnSuccess(session -> log.info("Created session {} for user {}", sessionId, username));
                })
            );
    }
    
    /**
     * Updates the last activity time for a session
     */
    public Mono<Void> updateSessionActivity(String sessionId) {
        return userSessionRepository.findBySessionId(sessionId)
            .flatMap(session -> {
                session.updateLastActivity();
                return userSessionRepository.save(session);
            })
            .then();
    }
    
    /**
     * Marks a session as disconnected when a client disconnects
     */
    public Mono<Void> disconnectSession(String sessionId) {
        log.info("Disconnecting session: {}", sessionId);
        return userSessionRepository.findBySessionId(sessionId)
            .flatMap(session -> {
                session.disconnect();
                return userSessionRepository.save(session)
                    .doOnSuccess(s -> log.info("Session {} marked as disconnected", sessionId));
            })
            .switchIfEmpty(
                Mono.fromRunnable(() -> log.warn("Attempted to disconnect non-existent session: {}", sessionId))
            )
            .then();
    }
    
    /**
     * Gets the username associated with a session ID
     */
    public Mono<String> getUsernameBySessionId(String sessionId) {
        return userSessionRepository.findBySessionId(sessionId)
                .map(UserSession::getUsername);
    }

    /**
     * Gets all active sessions for a specific user
     */
    public Flux<UserSession> getActiveSessionsForUser(String username) {
        return userSessionRepository.findActiveSessionsByUsername(username);
    }

    /**
     * Gets all active sessions
     */
    public Flux<UserSession> getAllActiveSessions() {
        return userSessionRepository.findByIsActive(true);
    }
    
    /**
     * Gets the count of active sessions
     */
    public Mono<Long> getActiveSessionCount() {
        return userSessionRepository.countActiveSessions();
    }
    
    /**
     * Removes old inactive sessions (cleanup method)
     */
    public Mono<Void> cleanupInactiveSessions() {
        return userSessionRepository.findByIsActive(false)
            .collectList()
            .flatMap(inactiveSessions -> {
                log.info("Cleaning up {} inactive sessions", inactiveSessions.size());
                return userSessionRepository.deleteAll(inactiveSessions);
            });
    }
}
