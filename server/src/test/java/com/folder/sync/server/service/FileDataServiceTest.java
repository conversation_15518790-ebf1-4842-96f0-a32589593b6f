package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.repository.FileDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FileDataServiceTest {

    @Mock
    private FileDataRepository fileDataRepository;

    private FileDataService fileDataService;

    @BeforeEach
    void setUp() {
        fileDataService = new FileDataService(fileDataRepository);
        ReflectionTestUtils.setField(fileDataService, "outputDir", "/tmp/test");
        ReflectionTestUtils.setField(fileDataService, "maxFileSize", 1000000L);
    }

    @Test
    void testGetExistingFileNames() {
        // Arrange
        FileData file1 = new FileData();
        file1.setOriginalFilePath("file1.txt");
        
        FileData file2 = new FileData();
        file2.setOriginalFilePath("file2.txt");
        
        FileData file3 = new FileData();
        file3.setOriginalFilePath("file1.txt"); // Duplicate
        
        List<FileData> fileDataList = Arrays.asList(file1, file2, file3);
        when(fileDataRepository.findAll()).thenReturn(fileDataList);

        // Act
        List<String> result = fileDataService.getExistingFileNames();

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.contains("file1.txt"));
        assertTrue(result.contains("file2.txt"));
        verify(fileDataRepository).findAll();
    }

    @Test
    void testGetExistingFileNames_EmptyRepository() {
        // Arrange
        when(fileDataRepository.findAll()).thenReturn(Arrays.asList());

        // Act
        List<String> result = fileDataService.getExistingFileNames();

        // Assert
        assertTrue(result.isEmpty());
        verify(fileDataRepository).findAll();
    }

    @Test
    void testGetFileCount() {
        // Arrange
        when(fileDataRepository.count()).thenReturn(5L);

        // Act
        long result = fileDataService.getFileCount();

        // Assert
        assertEquals(5L, result);
        verify(fileDataRepository).count();
    }
}
