package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.repository.FileDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FileDataServiceUserTest {

    @Mock
    private FileDataRepository fileDataRepository;

    @InjectMocks
    private FileDataService fileDataService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // Set the output directory to our temp directory
        ReflectionTestUtils.setField(fileDataService, "outputDir", tempDir.toString());
        ReflectionTestUtils.setField(fileDataService, "maxFileSize", 1000000L);
    }

    @Test
    void processFileData_WithUsername_ShouldCreateUserDirectory() throws IOException {
        // Given
        String username = "testuser";
        String filePath = "test.txt";
        String content = "test content";
        long fileSize = 100L;
        long lastModified = System.currentTimeMillis();

        when(fileDataRepository.save(any(FileData.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        fileDataService.processFileData(filePath, content, fileSize, lastModified, username);

        // Then
        Path userDir = tempDir.resolve(username);
        assertTrue(Files.exists(userDir), "User directory should be created");
        assertTrue(Files.isDirectory(userDir), "User path should be a directory");

        // Verify file was saved to database with username
        verify(fileDataRepository).save(argThat(fileData -> 
            username.equals(fileData.getUsername()) &&
            filePath.equals(fileData.getOriginalFilePath()) &&
            content.equals(fileData.getContent())
        ));
    }

    @Test
    void createUserDirectory_ShouldCreateDirectory() throws IOException {
        // Given
        String username = "newuser";

        // When
        fileDataService.createUserDirectory(username);

        // Then
        Path userDir = tempDir.resolve(username);
        assertTrue(Files.exists(userDir), "User directory should be created");
        assertTrue(Files.isDirectory(userDir), "User path should be a directory");
    }

    @Test
    void getExistingFileNamesForUser_ShouldReturnUserFiles() {
        // Given
        String username = "testuser";
        List<String> expectedFiles = Arrays.asList("file1.txt", "file2.txt");
        when(fileDataRepository.findDistinctOriginalFilePathsByUsername(username)).thenReturn(expectedFiles);

        // When
        List<String> result = fileDataService.getExistingFileNamesForUser(username);

        // Then
        assertEquals(expectedFiles, result);
        verify(fileDataRepository).findDistinctOriginalFilePathsByUsername(username);
    }

    @Test
    void getFileCountForUser_ShouldReturnUserFileCount() {
        // Given
        String username = "testuser";
        long expectedCount = 5L;
        when(fileDataRepository.countByUsername(username)).thenReturn(expectedCount);

        // When
        long result = fileDataService.getFileCountForUser(username);

        // Then
        assertEquals(expectedCount, result);
        verify(fileDataRepository).countByUsername(username);
    }

    @Test
    void getRecentFilesForUser_ShouldReturnUserRecentFiles() {
        // Given
        String username = "testuser";
        FileData file1 = new FileData();
        file1.setUsername(username);
        file1.setOriginalFilePath("recent1.txt");
        
        FileData file2 = new FileData();
        file2.setUsername(username);
        file2.setOriginalFilePath("recent2.txt");
        
        List<FileData> expectedFiles = Arrays.asList(file1, file2);
        when(fileDataRepository.findByUsernameOrderByReceivedAtDesc(username)).thenReturn(expectedFiles);

        // When
        List<FileData> result = fileDataService.getRecentFilesForUser(username);

        // Then
        assertEquals(expectedFiles, result);
        verify(fileDataRepository).findByUsernameOrderByReceivedAtDesc(username);
    }

    @Test
    void processFileData_WithoutUsername_ShouldUseDefaultUsername() {
        // Given
        String filePath = "test.txt";
        String content = "test content";
        long fileSize = 100L;
        long lastModified = System.currentTimeMillis();

        when(fileDataRepository.save(any(FileData.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        fileDataService.processFileData(filePath, content, fileSize, lastModified);

        // Then
        verify(fileDataRepository).save(argThat(fileData -> 
            "unknown".equals(fileData.getUsername())
        ));
    }
}
