package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.domain.model.UserSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SessionMonitorServiceTest {

    @Mock
    private UserSessionService userSessionService;

    @Mock
    private FileDataService fileDataService;

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @InjectMocks
    private SessionMonitorService sessionMonitorService;

    private UserSession testSession1;
    private UserSession testSession2;

    @BeforeEach
    void setUp() {
        testSession1 = new UserSession("session1", "user1");
        testSession1.setConnectedAt(LocalDateTime.now().minusMinutes(30));
        testSession1.setLastActivity(LocalDateTime.now().minusMinutes(5));
        testSession1.setIsActive(true);

        testSession2 = new UserSession("session2", "user2");
        testSession2.setConnectedAt(LocalDateTime.now().minusMinutes(15));
        testSession2.setLastActivity(LocalDateTime.now().minusMinutes(2));
        testSession2.setIsActive(true);
    }

    @Test
    void broadcastSessionStats_ShouldSendStatsMessage() {
        // Given
        List<UserSession> activeSessions = Arrays.asList(testSession1, testSession2);
        when(userSessionService.getAllActiveSessions()).thenReturn(activeSessions);
        when(fileDataService.getFileCount()).thenReturn(10L);

        // When
        sessionMonitorService.broadcastSessionStats();

        // Then
        verify(messagingTemplate).convertAndSend(
            eq("/topic/session-monitor"),
            argThat((SessionMonitorMessage message) -> {
                return "SESSION_STATS".equals(message.getType()) &&
                       message.getStats() != null &&
                       message.getStats().getTotalActiveSessions() == 2L &&
                       message.getStats().getTotalUsers() == 2L &&
                       message.getStats().getTotalFilesProcessed() == 10L;
            })
        );
    }

    @Test
    void broadcastSessionList_ShouldSendSessionListMessage() {
        // Given
        List<UserSession> activeSessions = Arrays.asList(testSession1, testSession2);
        when(userSessionService.getAllActiveSessions()).thenReturn(activeSessions);
        when(fileDataService.getFileCount()).thenReturn(5L);
        when(fileDataService.getFileCountForUser("user1")).thenReturn(3L);
        when(fileDataService.getFileCountForUser("user2")).thenReturn(2L);

        // When
        sessionMonitorService.broadcastSessionList();

        // Then
        verify(messagingTemplate).convertAndSend(
            eq("/topic/session-monitor"),
            argThat((SessionMonitorMessage message) -> {
                return "SESSION_LIST".equals(message.getType()) &&
                       message.getActiveSessions() != null &&
                       message.getActiveSessions().size() == 2 &&
                       message.getStats() != null;
            })
        );
    }

    @Test
    void broadcastSessionEvent_ShouldSendEventMessage() {
        // When
        sessionMonitorService.broadcastSessionEvent("CONNECTED", "session1", "user1", "User connected");

        // Then
        verify(messagingTemplate).convertAndSend(
            eq("/topic/session-monitor"),
            argThat((SessionMonitorMessage message) -> {
                return "SESSION_UPDATE".equals(message.getType()) &&
                       message.getEvent() != null &&
                       "CONNECTED".equals(message.getEvent().getEventType()) &&
                       "session1".equals(message.getEvent().getSessionId()) &&
                       "user1".equals(message.getEvent().getUsername()) &&
                       "User connected".equals(message.getEvent().getDetails());
            })
        );
    }

    @Test
    void getCurrentStats_ShouldReturnCorrectStats() {
        // Given
        List<UserSession> activeSessions = Arrays.asList(testSession1, testSession2);
        when(userSessionService.getAllActiveSessions()).thenReturn(activeSessions);
        when(fileDataService.getFileCount()).thenReturn(15L);

        // When
        SessionMonitorMessage.SessionStats stats = sessionMonitorService.getCurrentStats();

        // Then
        assertNotNull(stats);
        assertEquals(2L, stats.getTotalActiveSessions());
        assertEquals(2L, stats.getTotalUsers());
        assertEquals(15L, stats.getTotalFilesProcessed());
        assertTrue(stats.getAverageSessionDuration() > 0);
        assertNotNull(stats.getLastUpdate());
    }

    @Test
    void getCurrentSessions_ShouldReturnSessionInfoList() {
        // Given
        List<UserSession> activeSessions = Arrays.asList(testSession1, testSession2);
        when(userSessionService.getAllActiveSessions()).thenReturn(activeSessions);
        when(fileDataService.getFileCountForUser("user1")).thenReturn(5L);
        when(fileDataService.getFileCountForUser("user2")).thenReturn(3L);

        // When
        List<SessionMonitorMessage.SessionInfo> sessionInfos = sessionMonitorService.getCurrentSessions();

        // Then
        assertNotNull(sessionInfos);
        assertEquals(2, sessionInfos.size());
        
        SessionMonitorMessage.SessionInfo info1 = sessionInfos.get(0);
        assertEquals("session1", info1.getSessionId());
        assertEquals("user1", info1.getUsername());
        assertEquals(5L, info1.getFilesUploaded());
        assertTrue(info1.isActive());
        assertTrue(info1.getSessionDurationMinutes() > 0);
    }

    @Test
    void getCurrentStats_WithNoActiveSessions_ShouldReturnZeroStats() {
        // Given
        when(userSessionService.getAllActiveSessions()).thenReturn(Arrays.asList());
        when(fileDataService.getFileCount()).thenReturn(0L);

        // When
        SessionMonitorMessage.SessionStats stats = sessionMonitorService.getCurrentStats();

        // Then
        assertNotNull(stats);
        assertEquals(0L, stats.getTotalActiveSessions());
        assertEquals(0L, stats.getTotalUsers());
        assertEquals(0L, stats.getTotalFilesProcessed());
        assertEquals(0.0, stats.getAverageSessionDuration());
    }
}
