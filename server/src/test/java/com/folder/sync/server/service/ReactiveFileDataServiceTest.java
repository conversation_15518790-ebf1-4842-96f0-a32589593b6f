package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.ReactiveFileData;
import com.folder.sync.server.domain.repository.ReactiveFileDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReactiveFileDataServiceTest {

    @Mock
    private ReactiveFileDataRepository fileDataRepository;

    private ReactiveFileDataService fileDataService;

    @BeforeEach
    void setUp() {
        fileDataService = new ReactiveFileDataService(fileDataRepository);
        ReflectionTestUtils.setField(fileDataService, "outputDir", "/tmp/test");
        ReflectionTestUtils.setField(fileDataService, "maxFileSize", 1000000L);
    }

    @Test
    void testGetFileCount() {
        // Arrange
        when(fileDataRepository.count()).thenReturn(Mono.just(5L));

        // Act & Assert
        StepVerifier.create(fileDataService.getFileCount())
            .expectNext(5L)
            .verifyComplete();
    }

    @Test
    void testGetExistingFileNamesForUser() {
        // Arrange
        String username = "testuser";
        when(fileDataRepository.findDistinctOriginalFilePathsByUsername(username))
            .thenReturn(Flux.just("file1.txt", "file2.txt"));

        // Act & Assert
        StepVerifier.create(fileDataService.getExistingFileNamesForUser(username))
            .expectNext("file1.txt")
            .expectNext("file2.txt")
            .verifyComplete();
    }

    @Test
    void testGetFileCountForUser() {
        // Arrange
        String username = "testuser";
        when(fileDataRepository.countByUsername(username)).thenReturn(Mono.just(3L));

        // Act & Assert
        StepVerifier.create(fileDataService.getFileCountForUser(username))
            .expectNext(3L)
            .verifyComplete();
    }

    @Test
    void testGetRecentFilesForUser() {
        // Arrange
        String username = "testuser";
        ReactiveFileData file1 = ReactiveFileData.builder()
            .originalFilePath("file1.txt")
            .username(username)
            .build();
        ReactiveFileData file2 = ReactiveFileData.builder()
            .originalFilePath("file2.txt")
            .username(username)
            .build();

        when(fileDataRepository.findByUsernameOrderByReceivedAtDesc(username))
            .thenReturn(Flux.just(file1, file2));

        // Act & Assert
        StepVerifier.create(fileDataService.getRecentFilesForUser(username))
            .expectNext(file1)
            .expectNext(file2)
            .verifyComplete();
    }

    @Test
    void testCreateUserDirectory() {
        // Act & Assert
        StepVerifier.create(fileDataService.createUserDirectory("testuser"))
            .verifyComplete();
    }
}
