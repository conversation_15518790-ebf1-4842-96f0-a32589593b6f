package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.UserSession;
import com.folder.sync.server.domain.repository.UserSessionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserSessionServiceTest {

    @Mock
    private UserSessionRepository userSessionRepository;

    @InjectMocks
    private UserSessionService userSessionService;

    private UserSession testSession;

    @BeforeEach
    void setUp() {
        testSession = new UserSession("session123", "testuser");
    }

    @Test
    void createSession_NewSession_ShouldCreateAndSaveSession() {
        // Given
        when(userSessionRepository.findBySessionId("session123")).thenReturn(Optional.empty());
        when(userSessionRepository.save(any(UserSession.class))).thenReturn(testSession);

        // When
        UserSession result = userSessionService.createSession("session123", "testuser");

        // Then
        assertNotNull(result);
        assertEquals("session123", result.getSessionId());
        assertEquals("testuser", result.getUsername());
        assertTrue(result.getIsActive());
        verify(userSessionRepository).findBySessionId("session123");
        verify(userSessionRepository).save(any(UserSession.class));
    }

    @Test
    void createSession_ExistingSession_ShouldUpdateExistingSession() {
        // Given
        UserSession existingSession = new UserSession("session123", "olduser");
        existingSession.setIsActive(false);
        
        when(userSessionRepository.findBySessionId("session123")).thenReturn(Optional.of(existingSession));
        when(userSessionRepository.save(any(UserSession.class))).thenReturn(existingSession);

        // When
        UserSession result = userSessionService.createSession("session123", "newuser");

        // Then
        assertNotNull(result);
        assertEquals("session123", result.getSessionId());
        assertEquals("newuser", result.getUsername());
        assertTrue(result.getIsActive());
        verify(userSessionRepository).findBySessionId("session123");
        verify(userSessionRepository).save(existingSession);
    }

    @Test
    void getUsernameBySessionId_ExistingSession_ShouldReturnUsername() {
        // Given
        when(userSessionRepository.findBySessionId("session123")).thenReturn(Optional.of(testSession));

        // When
        Optional<String> result = userSessionService.getUsernameBySessionId("session123");

        // Then
        assertTrue(result.isPresent());
        assertEquals("testuser", result.get());
        verify(userSessionRepository).findBySessionId("session123");
    }

    @Test
    void getUsernameBySessionId_NonExistingSession_ShouldReturnEmpty() {
        // Given
        when(userSessionRepository.findBySessionId("nonexistent")).thenReturn(Optional.empty());

        // When
        Optional<String> result = userSessionService.getUsernameBySessionId("nonexistent");

        // Then
        assertFalse(result.isPresent());
        verify(userSessionRepository).findBySessionId("nonexistent");
    }

    @Test
    void disconnectSession_ExistingSession_ShouldMarkAsInactive() {
        // Given
        when(userSessionRepository.findBySessionId("session123")).thenReturn(Optional.of(testSession));
        when(userSessionRepository.save(any(UserSession.class))).thenReturn(testSession);

        // When
        userSessionService.disconnectSession("session123");

        // Then
        assertFalse(testSession.getIsActive());
        verify(userSessionRepository).findBySessionId("session123");
        verify(userSessionRepository).save(testSession);
    }

    @Test
    void getActiveSessionsForUser_ShouldReturnActiveSessions() {
        // Given
        List<UserSession> activeSessions = Arrays.asList(testSession);
        when(userSessionRepository.findActiveSessionsByUsername("testuser")).thenReturn(activeSessions);

        // When
        List<UserSession> result = userSessionService.getActiveSessionsForUser("testuser");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testuser", result.get(0).getUsername());
        verify(userSessionRepository).findActiveSessionsByUsername("testuser");
    }

    @Test
    void getActiveSessionCount_ShouldReturnCount() {
        // Given
        when(userSessionRepository.countActiveSessions()).thenReturn(5L);

        // When
        long result = userSessionService.getActiveSessionCount();

        // Then
        assertEquals(5L, result);
        verify(userSessionRepository).countActiveSessions();
    }
}
